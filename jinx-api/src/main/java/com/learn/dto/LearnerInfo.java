package com.learn.dto;

import com.learn.dto.toc.train.TrainDetailResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
public class LearnerInfo {
    /**
     * 总学习人数
     */
    private Integer total;

    /**
     * 已完成人数
     */
    private Integer completed;

    /**
     * 学习中人数
     */
    private Integer learning;

    /**
     * 未开始人数
     */
    private Integer notStart;

    /**
     * 学习人员列表
     */
    private List<LearnerItem> learnerItems;

    /**
     * 学习人员项
     */
    @Data
    public static class LearnerItem {
        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 用户头像
         */
        private String avatar;

        /**
         * 部门
         */
        private String department;

        /**
         * 学习状态：completed-已完成，learning-学习中
         */
        private String status;
    }
}
