package com.learn.dto.toc.course;

import com.learn.dto.LearnerInfo;
import com.learn.dto.course.sub.SeriesCourseFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 课程详情响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseDetailResponse {
    /**
     * 课程ID
     */
    private Long id;
    
    /**
     * 课程名称
     */
    private String title;
    
    /**
     * 课程类型
     */
    private String type;
    
    /**
     * 封面图URL
     */
    private String coverImage;
    
    /**
     * 课程简介
     */
    private String description;
    
    /**
     * 学分
     */
    private Integer credit;
    
    /**
     * 讲师信息
     */
    private InstructorDTO instructor;
    
    /**
     * 附件类型
     */
    private String appendixType;
    
    /**
     * 附件路径
     */
    private String appendixPath;
    
    /**
     * 课程时长（秒）
     */
    private Integer duration;
    
    /**
     * 用户学习进度
     */
    private UserProgressDTO userProgress;

    /**
     * 学习人员信息
     */
    private LearnerInfo learners;
    
    /**
     * 课程章节列表
     */
    private List<SeriesCourseFile> courseItems;
    
    /**
     * 讲师DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InstructorDTO {
        /**
         * 讲师ID
         */
        private Integer id;
        
        /**
         * 讲师姓名
         */
        private String name;
        
        /**
         * 讲师头像
         */
        private String avatar;
    }
    
    /**
     * 用户学习进度DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserProgressDTO {
        /**
         * 学习状态：not_started-未开始，learning-学习中，completed-已完成
         */
        private String status;
        
        /**
         * 学习进度（百分比）
         */
        private Integer progress;
        
        /**
         * 学习时长（秒）
         */
        private Integer studyDuration;
        
        /**
         * 最后学习时间
         */
        private String lastStudyTime;
    }

}
