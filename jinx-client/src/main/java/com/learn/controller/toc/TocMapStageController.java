package com.learn.controller.toc;

import com.learn.common.dto.util.UserTokenUtil;
import com.learn.common.util.UserContextHolder;
import com.learn.dto.toc.map.MapStageDetailResponse;
import com.learn.dto.user.UserInfoResponse;
import com.learn.service.toc.MapService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端学习地图阶段控制器
 */
@RestController
@RequestMapping("/api/map")
@Slf4j
public class TocMapStageController {

    @Autowired
    private MapService mapService;

    /**
     * 获取学习地图阶段详情
     *
     * @param mapId 学习地图ID
     * @param stageId 阶段ID
     * @param request HTTP请求
     * @return 学习地图阶段详情响应
     */
    @GetMapping("/stage/detail")
    public MapStageDetailResponse getMapStageDetail(@RequestParam("mapId") Integer mapId, @RequestParam("stageId") Integer stageId, 
                                                  HttpServletRequest request) {
        log.info("获取学习地图阶段详情，地图ID：{}，阶段ID：{}", mapId, stageId);
        
        // 参数校验
        if (mapId == null || mapId <= 0) {
            MapStageDetailResponse response = new MapStageDetailResponse();
            response.setCode(400);
            response.setMessage("参数错误：学习地图ID无效");
            return response;
        }
        
        if (stageId == null || stageId <= 0) {
            MapStageDetailResponse response = new MapStageDetailResponse();
            response.setCode(400);
            response.setMessage("参数错误：阶段ID无效");
            return response;
        }
        
        // 获取当前用户信息
        Long userId = UserContextHolder.getUserId();
        if (userId == null) {
            MapStageDetailResponse response = new MapStageDetailResponse();
            response.setCode(401);
            response.setMessage("未登录或登录已过期");
            return response;
        }
        
        // 调用服务获取学习地图阶段详情
        return mapService.getMapStageDetail(mapId.longValue(), stageId.longValue(), userId);
    }
}
