package com.learn.controller.common;

import com.learn.dto.LearnerInfo;
import com.learn.dto.LearnerQueryReq;
import com.learn.dto.common.ApiResponse;
import com.learn.service.LearnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@RestController
@RequestMapping("/api/learner")
@Slf4j
public class LearnerController {

    @Autowired
    private LearnerService learnerService;

    @PostMapping("/total")
    public ApiResponse<LearnerInfo> total(@RequestBody LearnerQueryReq request) {
        // 参数校验
        if (request.getBizId() == null || request.getBizId() <= 0) {
            return ApiResponse.error(400, "业务ID不能为空且必须大于0");
        }

        if (StringUtils.isBlank(request.getBizType())) {
            return ApiResponse.error(400, "业务类型不能为空");
        }

        return ApiResponse.success(learnerService.total(request));
    }

    @PostMapping("/list")
    public ApiResponse<LearnerInfo> list(@RequestBody LearnerQueryReq request) {
        // 参数校验
        if (request.getBizId() == null || request.getBizId() <= 0) {
            return ApiResponse.error(400, "业务ID不能为空且必须大于0");
        }

        if (StringUtils.isBlank(request.getBizType())) {
            return ApiResponse.error(400, "业务类型不能为空");
        }

        return ApiResponse.success(learnerService.query(request));
    }

}
