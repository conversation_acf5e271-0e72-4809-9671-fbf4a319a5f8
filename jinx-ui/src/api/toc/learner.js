import request from '@/utils/request';

/**
 * 获取学习人员总数
 * @param {Object} params - 参数
 * @param {string} params.bizType - 业务类型
 * @param {number} params.bizId - 业务ID
 */
export function getLearnerTotal(params) {
  return request({
    url: '/api/learner/total',
    method: 'post',
    data: params
  });
}

/**
 * 获取学习人员列表
 * @param {Object} params - 参数
 * @param {string} params.bizType - 业务类型
 * @param {number} params.bizId - 业务ID
 * @param {string} params.status - 状态：'' 全部, 'not_started' 未开始, 'learning' 学习中, 'completed' 已完成
 * @param {number} params.pageSize - 每页数量，默认20
 * @param {number} params.pageNum - 页码，默认1
 */
export function getLearnerList(params) {
  return request({
    url: '/api/learner/list',
    method: 'post',
    data: {
      pageSize: 20,
      pageNum: 1,
      ...params
    }
  });
}