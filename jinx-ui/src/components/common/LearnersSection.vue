<template>
  <div class="learners-section">
    <h3 class="learners-title">学习人员 <span class="learner-count">({{ totalCount }})</span></h3>

    <!-- 人员筛选标签 -->
    <div class="learners-tabs">
      <div class="tab-item" :class="{ active: activeLearnerTab === '' }" @click="changeTab('')">
        全部 ({{ learnerCounts.total }})
      </div>
      <div class="tab-item" :class="{ active: activeLearnerTab === 'completed' }" @click="changeTab('completed')">
        已完成 ({{ learnerCounts.completed }})
      </div>
      <div class="tab-item" :class="{ active: activeLearnerTab === 'learning' }" @click="changeTab('learning')">
        进行中 ({{ learnerCounts.learning }})
      </div>
      <div class="tab-item" :class="{ active: activeLearnerTab === 'not_started' }" @click="changeTab('not_started')">
        未开始 ({{ learnerCounts.notStart }})
      </div>
    </div>

    <div class="learners-list">
      <a-spin :spinning="loading">
        <a-empty v-if="!learnerLists.length && !loading" description="暂无学习人员" />
        <div v-else-if="learnerLists.length > 0" class="learners-grid">
        <div v-for="(learner, index) in learnerLists" :key="index" class="learner-item">
          <a-tooltip :title="learner.nickname + (learner.department ? ' - ' + learner.department : '')">
            <a-badge :dot="learner.status === 'completed'" :color="getLearnerStatusColor(learner.status)">
              <a-avatar :src="learner.avatar" v-if="learner.avatar">
                <template #icon v-if="!learner.avatar">
                  <UserOutlined />
                </template>
              </a-avatar>
              <a-avatar v-else>
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
            </a-badge>
          </a-tooltip>
          <div class="learner-name">{{ learner.nickname }}</div>
          <div class="learner-status">
            <a-tag :color="getLearnerStatusTagColor(learner.status)">
              {{ getLearnerStatusText(learner.status) }}
            </a-tag>
          </div>
        </div>

        <!-- 省略圆圈 -->
        <div v-if="shouldShowEllipsis" class="learner-item ellipsis-item">
          <div class="ellipsis-circle">
            <span>...</span>
          </div>
        </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, watch } from 'vue';
import { UserOutlined } from '@ant-design/icons-vue';
import { getLearnerTotal, getLearnerList } from '@/api/toc/learner';

export default defineComponent({
  name: 'LearnersSection',
  components: {
    UserOutlined
  },
  props: {
    bizType: {
      type: String,
      required: true
    },
    bizId: {
      type: [String, Number],
      required: true
    }
  },
  setup(props) {
    const activeLearnerTab = ref('completed');
    const loading = ref(false);

    // 学习人员数据
    const learnerCounts = ref({
      completed: 0,
      learning: 0,
      notStart: 0,
      total: 0
    });

    const learnerLists = ref([]);


    // 是否需要显示省略圆圈
    const shouldShowEllipsis = computed(() => {
      return learnerLists.value.length >= 20;
    });


    // 获取学习人员状态颜色
    const getLearnerStatusColor = (status) => {
      if (status === 'completed') return '#52c41a';
      if (status === 'learning') return '#1890ff';
      return '#faad14';
    };

    // 获取学习人员状态标签颜色
    const getLearnerStatusTagColor = (status) => {
      if (status === 'completed') return 'success';
      if (status === 'learning') return 'processing';
      return 'warning';
    };

    // 获取学习人员状态文本
    const getLearnerStatusText = (status) => {
      if (status === 'completed') return '已完成';
      if (status === 'learning') return '学习中';
      return '未开始';
    };

    // 获取学习人员总数
    const fetchLearnerTotal = async () => {
      try {
        const response = await getLearnerTotal({
          bizType: props.bizType,
          bizId: props.bizId
        });

        if (response.code === 200 && response.data) {
          learnerCounts.value = response.data;
        }
      } catch (error) {
        console.error('获取学习人员总数失败:', error);
      }
    };

    // 获取学习人员列表
    const fetchLearnerList = async (status) => {
      try {
        loading.value = true;
        const response = await getLearnerList({
          bizType: props.bizType,
          bizId: props.bizId,
          status: status
        });

        if (response.code === 200 && response.data) {
          learnerLists.value = response.data.learnerItems || [];
        }
      } catch (error) {
        console.error(`获取${status}学习人员列表失败:`, error);
      } finally {
        loading.value = false;
      }
    };

    const changeTab = async (status) => {
      if(status === activeLearnerTab.value) {
        return;
      }
      activeLearnerTab.value = status;
      await fetchLearnerList(status);
    };

    // 初始化数据
    const initData = async () => {
      await fetchLearnerTotal();
      // 默认加载已完成的学习人员列表
      await fetchLearnerList('completed');
    };


    // 监听props变化，重新加载数据
    watch(() => [props.bizType, props.bizId], () => {
      if (props.bizType && props.bizId) {
        initData();
      }
    }, { immediate: false });

    onMounted(() => {
      if (props.bizType && props.bizId) {
        initData();
      }
    });

    return {
      activeLearnerTab,
      loading,
      learnerCounts,
      learnerLists,
      shouldShowEllipsis,
      getLearnerStatusColor,
      getLearnerStatusTagColor,
      getLearnerStatusText,
      changeTab
    };
  }
});
</script>

<style scoped>
.learners-section {
  margin-top: 24px;
  padding: 24px;
}

.learners-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.learner-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: normal;
  margin-left: 8px;
}

.learners-tabs {
  display: flex;
  border-bottom: 1px solid #E5E7EB;
  margin-bottom: 24px;
  position: relative;
}

.tab-item {
  padding: 8px 0;
  margin-right: 32px;
  font-size: 16px;
  color: #6B7280;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  color: #1890FF;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3B82F6;
}

.learners-list {
  position: relative;
}

.learners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .learners-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 12px;
  }

  .learners-section {
    margin-top: 20px;
    padding: 12px;
  }

  .learners-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .learners-tabs {
    margin-bottom: 16px;
  }

  .tab-item {
    font-size: 14px;
    margin-right: 24px;
  }
}

.learner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #f9fafb;
}

.learner-item:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.learner-item .ant-avatar {
  margin-bottom: 8px;
  background: #1890ff;
}

.learner-name {
  font-size: 14px;
  color: #3d3d3d;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  margin-bottom: 4px;
}

.learner-status {
  margin-top: 4px;
}

.learner-item .ant-tag {
  margin: 0 !important;
}

.ellipsis-item {
  background-color: #f0f0f0 !important;
  cursor: default;
}

.ellipsis-item:hover {
  transform: none !important;
  box-shadow: none !important;
  background-color: #f0f0f0 !important;
}

.ellipsis-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 16px;
  color: #666;
  font-weight: bold;
}
</style>