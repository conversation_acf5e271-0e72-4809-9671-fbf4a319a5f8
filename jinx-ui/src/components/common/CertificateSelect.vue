<template>
  <a-select v-model:value="selectedValue" :options="certificateList" :filter-option="false" :placeholder="placeholder"
    :allow-clear="allowClear" :show-search="true" :multiple="multiple"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" style="width: 100%" @search="handleSearch"
    @change="handleChange" v-bind="$attrs" v-show="!templateUrl" />
    <!-- 预览  展示预览图 名称 支持删除-->
    <div v-if="!!templateUrl" class="certificate-preview">
      <a-image :width="100" :src="templateUrl" />
      <span class="certificate-name">{{ certificateList.find(item => item.value === selectedValue)?.label }}</span>
      <a-button type="link" @click="handleDelete">删除</a-button>
    </div>
</template>

<script>
import { ref, watch, defineComponent, computed } from 'vue';
import { debounce } from 'throttle-debounce';
import { getCertificateList } from '@/api/certificate';

export default defineComponent({
  name: 'UserList',
  props: {
    value: {
      type: [String, Number, Array],
      default: undefined
    },
    placeholder: {
      type: String,
      default: '请选择证书'
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    treeCheckable: {
      type: Boolean,
      default: false
    },
    showCheckedStrategy: {
      type: String,
      default: 'SHOW_ALL'
    },
    treeDefaultExpandAll: {
      type: Boolean,
      default: false
    },
    treeNodeFilterProp: {
      type: String,
      default: 'title'
    },
    // 是否只查询管理员
    queryAdmin: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const selectedValue = ref(props.value);
    const certificateList = ref([]);

    const templateUrl = computed(() => {
      const certificate = certificateList.value.find(item => item.value === selectedValue.value);
      console.log(certificate,'2222222222222');
      return certificate?.templateUrl;
    });

    /** 多选回填触发初始数据加载；不建议拉取太多数据 */
    const getFallBackData = ({ ids }) => {
      return new Promise(async(resolve, reject) => {
        try {
          const res = await getCertificateList({
            pageNum: 1,
            pageSize: 10,
            ids,
            containDeleted: true
          });
          if (res.code === 200 && res.data) {
            const fallbackData = res.data?.list?.map(item => ({
              label: item.name,
              value: item.id,
              templateUrl: item.templateUrl
            })) || [];

            // 防止重复添加相同的用户选项
            const existingIds = certificateList.value.map(item => item.value);
            const uniqueFallbackUsers = fallbackData.filter(item => !existingIds.includes(item.value));

            certificateList.value = [...certificateList.value, ...uniqueFallbackUsers];
            resolve(fallbackData);
          } else {
            reject(new Error('Failed to get certificate data'));
          }
        } catch (error) {
          console.error('获取用户回填数据失败:', error);
          reject(error);
        }
      });
    };

    // 删除
    const handleDelete = () => {
      selectedValue.value = undefined;
      emit('update:value', undefined);
      emit('change', undefined);
    };

    // 获取证书列表
    const fetchList = debounce(200, async (name) => {
      try {
        const res = await getCertificateList({
          name: name,
          pageNum: 1,
          pageSize: 10,
          queryAdmin: props.queryAdmin
        });
        if (res.code === 200 && res.data) {
          console.log('获取证书列表:', res.data);
          certificateList.value = res.data?.list?.map(item => ({
            label: item.name,
            value: item.id,
            templateUrl: item.templateUrl
          })) || [];
        }
      } catch (error) {
        console.error('获取证书列表失败:', error);
      }
    });

    // 处理搜索
    const handleSearch = (value) => {
      console.log('搜索:', value);
      fetchList(value);
    };

    // 处理选择变更
    const handleChange = (value) => {
      emit('update:value', value);
      emit('change', value);
    };

    // 监听外部value变化
    watch(() => props.value, (newVal) => {
      selectedValue.value = newVal;

      // 当有初始值时，获取对应用户数据进行回填
      if (newVal && ((Array.isArray(newVal) && newVal.length > 0) || (!Array.isArray(newVal) && newVal))) {
        const userIds = Array.isArray(newVal) ? newVal : [newVal];
        getFallBackData({ userIds }).catch(err => {
          console.error('回填用户数据失败:', err);
        });
      }
    }, { immediate: true });

    // 初始化时获取分类数据
    fetchList();

    return {
      selectedValue,
      certificateList,
      templateUrl,
      handleChange,
      handleSearch,
      handleDelete
    };
  }
});
</script>

<style scoped>
/* 可以添加自定义样式 */
.certificate-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  padding: 10px;
  margin-bottom: 10px;
  width: 100%;
}
.certificate-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>