<template>
  <a-layout class="role-edit">
    <!-- 使用 HeaderComponent -->
    <HeaderComponent activeKey="MANAGEMENT_CENTER" :showSearch="false" />

    <a-layout>
      <!-- 使用 SiderComponent -->
      <SiderComponent
        v-model:collapsed="collapsed"
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        @category-modal-show="showCategoryModal"
      />

      <a-layout-content class="content">
        <a-page-header
          title="编辑角色"
          sub-title="修改角色信息"
          @back="goBack"
        />
      <a-card>
        <a-spin :spinning="loading">
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            layout="vertical"
          >
            <a-form-item label="角色名称" name="roleName">
              <a-input v-model:value="formState.roleName" placeholder="请输入角色名称" />
            </a-form-item>

            <a-form-item label="角色编码" name="roleCode">
              <a-input v-model:value="formState.roleCode" placeholder="请输入角色编码" />
            </a-form-item><a-form-item label="角色描述" name="roleDescription">
              <a-textarea
                v-model:value="formState.roleDescription"
                placeholder="请输入角色描述"
                :rows="4"
              />
            </a-form-item>

            <a-form-item label="权限设置" name="permissionIds">
            <a-spin :spinning="permissionsLoading">
            <a-tree
            v-if="permissionTree.length > 0"
            checkable
            :tree-data="permissionTree"
            v-model:checked-keys="formState.permissionIds"
            :default-expanded-keys="expandedKeys"
              :check-strictly="true"
              @check="onPermissionCheck"
              />
                <a-empty v-else description="暂无权限数据" />
            </a-spin>
          </a-form-item>

            <a-form-item>
              <a-button type="primary" @click="handleSubmit" :loading="submitting">提交</a-button>
              <a-button style="margin-left: 10px" @click="goBack">取消</a-button>
            </a-form-item>
          </a-form>
        </a-spin>
      </a-card>
    </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { updateRole, getRoleList } from '@/api/role';
import { getAllPermissions } from '@/api/permission';
import HeaderComponent from '@/components/common/HeaderComponent.vue';
import SiderComponent from '@/components/common/SiderComponent.vue';

export default defineComponent({
  name: 'RoleEditPage',
  components: {
    HeaderComponent,
    SiderComponent
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const formRef = ref(null);
    const loading = ref(false);
    const submitting = ref(false);
    const permissionsLoading = ref(false);
    const permissionTree = ref([]);
    const expandedKeys = ref([]);
    const collapsed = ref(false);
    const selectedKeys = ref(['role']);
    const openKeys = ref(['system']);

    // 获取角色ID
    const roleId = route.params.id;

    // 表单数据
    const formState = reactive({
      id: null,
      roleName: '',
      roleCode: '',
      roleDescription: '',
      permissionIds: []
    });

    // 表单验证规则
    const rules = {
      roleName: [
        { required: true, message: '请输入角色名称', trigger: 'blur' },
        { max: 50, message: '角色名称不能超过50个字符', trigger: 'blur' }
      ],
      roleCode: [
        { required: true, message: '请输入角色编码', trigger: 'blur' },
        { max: 50, message: '角色编码不能超过50个字符', trigger: 'blur' },
        { pattern: /^[A-Z0-9_]+$/, message: '角色编码只能包含大写字母、数字和下划线', trigger: 'blur' }
      ],
      roleDescription: [
        { max: 255, message: '角色描述不能超过255个字符', trigger: 'blur' }
      ],
      permissionIds: [
        { type: 'array', required: true, message: '请至少选择一个权限', trigger: 'change' }
      ]
    };

    // 获取角色信息
    const fetchRoleInfo = async () => {
      loading.value = true;
      try {
        const res = await getRoleList({ id: route.params.id });

        if (res.code === 200 && res.data) {
          const roles = res.data.data || [];
          const role = roles.find(r => r.id === Number(roleId));

          if (role) {
            formState.id = role.id;
            formState.roleName = role.roleName;
            formState.roleCode = role.roleCode;
            formState.roleDescription = role.roleDescription;
            formState.permissionIds = role.permissionIds || [];
          } else {
            message.error('未找到角色信息');
            router.push('/role/management');
          }
        } else {
          message.error(res.message || '获取角色信息失败');
          router.push('/role/management');
        }
      } catch (error) {
        console.error('获取角色信息失败:', error);
        router.push('/role/management');
      } finally {
        loading.value = false;
      }
    };

    // 获取所有权限
    const fetchPermissions = async () => {
      permissionsLoading.value = true;
      try {
        const res = await getAllPermissions();

        if (res.code === 200 && res.data) {
          permissionTree.value = formatPermissionTree(res.data);

          // 设置默认展开的节点
          const keys = [];
          collectKeys(permissionTree.value, keys);
          expandedKeys.value = keys;
        } else {
          message.error(res.message || '获取权限列表失败');
        }
      } catch (error) {
        console.error('获取权限列表失败:', error);
      } finally {
        permissionsLoading.value = false;
      }
    };

    // 格式化权限树
    const formatPermissionTree = (permissions) => {
      return permissions.map(permission => ({
        title: permission.name,
        key: permission.id,
        children: permission.children && permission.children.length > 0
          ? formatPermissionTree(permission.children)
          : undefined
      }));
    };

    // 收集所有节点的key，用于默认展开
    const collectKeys = (tree, keys) => {
      tree.forEach(node => {
        keys.push(node.key);
        if (node.children && node.children.length > 0) {
          collectKeys(node.children, keys);
        }
      });
    };

    // 提交表单
    const handleSubmit = () => {
      formRef.value.validate().then(async () => {
        submitting.value = true;
        try {
          const res = await updateRole(formState);

          if (res.code === 200) {
            message.success('更新角色成功');
            router.push('/role/management');
          } else {
            message.error(res.message || '更新角色失败');
          }
        } catch (error) {
          console.error('更新角色失败:', error);
        } finally {
          submitting.value = false;
        }
      }).catch(error => {
        console.log('验证失败:', error);
      });
    };

    // 返回上一页
    const goBack = () => {
      router.push('/role/management');
    };

    onMounted(() => {
      fetchRoleInfo();
      fetchPermissions();
    });

    // 权限选择处理函数
    const onPermissionCheck = (checkedKeys, info) => {
      // 处理 checkedKeys 参数，确保它是一个数组
      let currentCheckedKeys = [];
      if (Array.isArray(checkedKeys)) {
        currentCheckedKeys = [...checkedKeys];
      } else if (checkedKeys && typeof checkedKeys === 'object') {
        // 如果 checkedKeys 是对象，提取 checked 属性
        currentCheckedKeys = Array.isArray(checkedKeys.checked) ? [...checkedKeys.checked] : [];
      }
      
      let newCheckedKeys = [...currentCheckedKeys];
      const nodeKey = info.node.key;
      
      // 如果是选中操作
      if (info.checked) {
        // 1. 选中当前节点的所有子孙节点
        const descendantKeys = getDescendantKeys(nodeKey, permissionTree.value);
        descendantKeys.forEach(key => {
          if (!newCheckedKeys.includes(key)) {
            newCheckedKeys.push(key);
          }
        });
        
        // 2. 级联勾选祖先节点
        const ancestorKeys = getAncestorKeys(nodeKey, permissionTree.value);
        ancestorKeys.forEach(key => {
          if (!newCheckedKeys.includes(key)) {
            newCheckedKeys.push(key);
          }
        });
      } else {
        // 取消选中操作
        // 1. 取消选中所有子孙节点
        const descendantKeys = getDescendantKeys(nodeKey, permissionTree.value);
        newCheckedKeys = newCheckedKeys.filter(key => !descendantKeys.includes(key));
        
        // 2. 修复：父节点不会因为子节点全部取消选中而自动取消选中
        // 注释掉原来的祖先节点自动取消逻辑
        // const ancestorKeys = getAncestorKeys(nodeKey, permissionTree.value);
        // ancestorKeys.forEach(ancestorKey => {
        //   const childrenKeys = getDirectChildrenKeys(ancestorKey, permissionTree.value);
        //   const hasCheckedChild = childrenKeys.some(childKey => newCheckedKeys.includes(childKey));
        //   if (!hasCheckedChild) {
        //     newCheckedKeys = newCheckedKeys.filter(key => key !== ancestorKey);
        //   }
        // });
      }
      
      formState.permissionIds = newCheckedKeys;
    };

    // 获取祖先节点的key
    const getAncestorKeys = (nodeKey, tree, ancestors = []) => {
      for (const node of tree) {
        if (node.key === nodeKey) {
          return ancestors;
        }
        if (node.children && node.children.length > 0) {
          const found = getAncestorKeys(nodeKey, node.children, [...ancestors, node.key]);
          if (found.length > 0 || node.children.some(child => child.key === nodeKey)) {
            return [...ancestors, node.key];
          }
        }
      }
      return [];
    };

    // 获取后代节点的key
    const getDescendantKeys = (nodeKey, tree) => {
      const descendants = [];
      
      const findNode = (nodes) => {
        for (const node of nodes) {
          if (node.key === nodeKey) {
            collectDescendants(node, descendants);
            return true;
          }
          if (node.children && node.children.length > 0) {
            if (findNode(node.children)) {
              return true;
            }
          }
        }
        return false;
      };
      
      findNode(tree);
      return descendants;
    };

    // 收集后代节点
    const collectDescendants = (node, descendants) => {
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          descendants.push(child.key);
          collectDescendants(child, descendants);
        });
      }
    };

    // 获取直接子节点的key
    const getDirectChildrenKeys = (parentKey, tree) => {
      const children = [];
      
      const findParentNode = (nodes) => {
        for (const node of nodes) {
          if (node.key === parentKey) {
            if (node.children && node.children.length > 0) {
              node.children.forEach(child => {
                children.push(child.key);
              });
            }
            return true;
          }
          if (node.children && node.children.length > 0) {
            if (findParentNode(node.children)) {
              return true;
            }
          }
        }
        return false;
      };
      
      findParentNode(tree);
      return children;
    };

    // 显示类目管理弹窗
    const showCategoryModal = () => {
      // 这里可以添加类目管理弹窗的逻辑，如果需要的话
    };

    return {
      formRef,
      formState,
      rules,
      loading,
      submitting,
      permissionsLoading,
      permissionTree,
      expandedKeys,
      collapsed,
      selectedKeys,
      openKeys,
      handleSubmit,
      goBack,
      showCategoryModal,
      onPermissionCheck
    };
  }
});
</script>

<style scoped>
.role-edit {
  height: 100%;
  background: #f0f2f5;
}

.header {
  background: #fff;
  padding: 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 1;
}

.content {
  padding: 24px;
  overflow-y: auto;
}
</style>
