<template>
  <a-layout class="learning-map-edit-layout">
    <!-- 使用 HeaderComponent -->
    <HeaderComponent activeKey="MANAGEMENT_CENTER" :showSearch="false" /><a-layout><!-- 使用 SiderComponent -->
      <SiderComponent v-model:collapsed="collapsed" v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys"
        @category-modal-show="showCategoryModal" />

      <!-- 内容区 -->
      <a-layout-content class="content">
        <!-- 标题栏 -->
        <div class="content-header">
          <PageBreadcrumb />
        </div>

        <!-- 表单区域 -->
        <div class="form-container">
          <a-form ref="mapFormRef" class="form" :model="mapForm" :rules="rules"
            :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 12 }">
            <a-spin :spinning="loading" style="height: 100%;">
              <!-- 基本信息 -->
              <div class="section-title">基本信息</div>
              <a-row :gutter="16">
                <a-col :span="16">
                  <a-form-item label="地图名称" name="name"><a-input v-model:value="mapForm.name" placeholder="请输入地图名称"
                      :maxlength="100" show-count /></a-form-item>
                </a-col>
              </a-row><a-form-item label="封面图" name="cover">
                <ImageCropper v-model:value="mapForm.cover" :width="640" :height="360" label="选择图片"
                  tip="支持JPG、PNG格式，裁剪为 640x360 像素" extra="请上传清晰美观的封面图，作为学习地图的主要视觉元素"
                  @crop-success="handleCropSuccess" />
              </a-form-item>

              <a-form-item label="地图简介" name="introduction">
                <a-textarea v-model:value="mapForm.introduction" placeholder="请输入地图简介" :rows="4" :maxlength="500"
                  show-count />
              </a-form-item>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="分类" name="categoryIds">
                    <CategoryTreeSelect v-model:value="mapForm.categoryIds" />
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 学分规则 -->
              <div class="section-title">学分规则</div>
              <a-form-item name="creditRule">
                <a-radio-group v-model:value="mapForm.creditRule">
                  <a-radio :value="0">整体发放</a-radio>
                  <a-radio :value="1">按阶段发放</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-row :gutter="16" v-if="mapForm.creditRule === 0">
                <a-col :span="8">
                  <a-form-item label="学分" name="requiredCredit">
                    <a-input-number v-model:value="mapForm.requiredCredit" :min="0" :max="100" :precision="1"
                      style="width: 100%" />
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="8">
                  <a-form-item label="选修学分" name="electiveCredit">
                    <a-input-number v-model:value="mapForm.electiveCredit" :min="0" :max="100" :precision="1"
                      style="width: 100%" />
                  </a-form-item>
                </a-col> -->
              </a-row>

              <!-- 证书规则 -->
              <div class="section-title">证书规则</div>
              <a-form-item name="certificateRule">
                <a-radio-group v-model:value="mapForm.certificateRule">
                  <a-radio :value="0">不发放</a-radio>
                  <a-radio :value="1">整体发放</a-radio>
                  <a-radio :value="2">按阶段发放</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item v-if="mapForm.certificateRule === 1" label="证书" name="certificateId">
                <CertificateSelect v-model:value="mapForm.certificateId" />
              </a-form-item>

              <!-- 学习规则 -->
              <div class="section-title">学习规则</div>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="开放时间" name="openTime">
                    <a-range-picker v-model:value="dateRange" format="YYYY-MM-DD HH:mm:ss" style="width: 100%"
                      @change="handleDateRangeChange" :show-time="{
                        hideDisabledOptions: true,
                        defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('11:59:59', 'HH:mm:ss')],
                      }" :disabledDate="disabledDate" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="解锁方式" name="unlockMode">
                    <a-select v-model:value="mapForm.unlockMode" placeholder="请选择解锁方式" style="width: 100%">
                      <a-select-option :value="0">按阶段和任务</a-select-option>
                      <a-select-option :value="1">按阶段</a-select-option>
                      <a-select-option :value="2">自由模式</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- <a-form-item label="主题皮肤" name="theme">
                <a-radio-group v-model:value="mapForm.theme" button-style="solid">
                  <a-radio-button value="business">商务简约</a-radio-button>
                  <a-radio-button value="tech">动感科技</a-radio-button>
                  <a-radio-button value="farm">农场时光</a-radio-button>
                  <a-radio-button value="chinese">中国元素</a-radio-button>
                  <a-radio-button value="list">列表模式</a-radio-button>
                </a-radio-group>
              </a-form-item> -->

              <!-- <a-form-item label="钉钉群" name="dingtalkGroup">
                <a-radio-group v-model:value="mapForm.dingtalkGroup">
                  <a-radio :value="0">不创建</a-radio>
                  <a-radio :value="1">创建钉钉群</a-radio>
                </a-radio-group>
              </a-form-item> -->

              <!-- 阶段管理 -->

              <div class="section-title sticky-title">
                阶段管理
                <a-button type="primary" size="small" style="margin-left: 16px;" @click="handleAddStage">
                  <plus-outlined />添加阶段
                </a-button>
              </div>

              <div v-if="mapForm.stages.length === 0" class="empty-stages">
                <a-empty description="暂无阶段，请添加阶段" />
              </div>

              <div v-else class="stages-container">
                <a-collapse v-model:activeKey="activeStageKeys">
                  <a-collapse-panel v-for="(stage, stageIndex) in mapForm.stages"
                    :key="stage.key || stage.id || `new-${stageIndex}`" :header="getStageHeader(stage, stageIndex)">
                    <template #extra>
                      <a-space>
                        <a-button type="primary" size="small" @click.stop="handleAddTask(stageIndex)">
                          <plus-outlined />添加任务
                        </a-button>
                        <a-button type="primary" size="small" danger @click.stop="handleDeleteStage(stageIndex)">
                          <delete-outlined />删除阶段
                        </a-button>
                      </a-space>
                    </template>

                    <!-- 阶段表单 -->
                    <a-form>
                      <a-row :gutter="16">
                        <a-col :span="12">
                          <a-form-item label="阶段名称" required>
                            <a-input v-model:value="stage.name" placeholder="请输入阶段名称" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="12">
                          <a-form-item label="阶段顺序">
                            <a-input-number v-model:value="stage.stageOrder" :min="1" style="width: 100%" />
                          </a-form-item>
                        </a-col>
                      </a-row>

                      <a-form-item label="开放类型">
                        <a-radio-group v-model:value="stage.openType">
                          <a-radio :value="0">不设置</a-radio>
                          <a-radio :value="1">固定时间</a-radio>
                          <a-radio :value="2">学习期限</a-radio>
                        </a-radio-group>
                      </a-form-item>

                      <a-form-item v-if="stage.openType === 1" label="开放时间">
                        <a-range-picker v-model:value="stage.stageTimeRange" format="YYYY-MM-DD HH:mm:ss"
                          style="width: 100%" @change="(dates) => handleStageTimeChange(dates, stageIndex)" :show-time="{
                            hideDisabledOptions: true,
                            defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('11:59:59', 'HH:mm:ss')],
                          }" :disabledDate="disabledDate" />
                      </a-form-item>

                      <a-form-item v-if="stage.openType === 2" label="学习期限(天)">
                        <a-input-number v-model:value="stage.durationDays" :min="1" style="width: 100%" />
                      </a-form-item><a-form-item v-if="mapForm.creditRule === 1" label="阶段学分">
                        <a-input-number v-model:value="stage.credit" :min="0" :precision="1"
                          style="width: 100%" /></a-form-item>

                      <a-form-item v-if="mapForm.certificateRule === 2" label="阶段证书">
                        <CertificateSelect v-model:value="stage.certificateId" />
                      </a-form-item>

                      <!-- 任务列表 -->
                      <div class="tasks-section">
                        <div class="section-subtitle">任务列表</div>
                        <div v-if="!stage.tasks || stage.tasks.length === 0" class="empty-tasks"><a-empty
                            description="暂无任务，请添加任务" />
                        </div>
                        <a-table v-else :dataSource="stage.tasks" :columns="taskColumns" :pagination="false"
                          size="small" :rowKey="record => record.key || record.id || record.contentId">
                          <template #bodyCell="{ column, record, index }">
                            <template v-if="column.key === 'type'">
                              {{ getTaskTypeName(record.type) }}
                            </template>
                            <template v-if="column.key === 'isRequired'">
                              <a-switch v-model:checked="record.isRequired" :checkedChildren="'必修'"
                                :unCheckedChildren="'选修'" />
                            </template>
                            <template v-if="column.key === 'action'">
                              <a-space>
                                <a-button type="text" size="small" @click="moveTask(stageIndex, index, -1)"
                                  :disabled="index === 0">
                                  <arrow-up-outlined /></a-button>
                                <a-button type="text" size="small" @click="moveTask(stageIndex, index, 1)"
                                  :disabled="index === stage.tasks.length - 1">
                                  <arrow-down-outlined /></a-button>
                                <a-button type="link" danger @click="handleDeleteTask(stageIndex, index)">
                                  <delete-outlined />
                                </a-button>
                              </a-space>
                            </template>
                          </template>
                        </a-table>
                      </div>
                    </a-form>
                  </a-collapse-panel>
                </a-collapse>
              </div>

              <!-- 可见范围设置 -->
              <div class="section-title">可见范围设置</div>
              <a-form-item name="visibility" label="可见范围">
                <a-radio-group v-model:value="mapForm.visibility.type"
                  :options="Object.values(VISIBILITY_TYPE).map(item => ({ label: item.name, value: item.key }))">
                </a-radio-group>
                <div v-if="mapForm.visibility.type === 'PART'" style="margin-top: 16px">
                  <a-button type="primary" @click="showOrgUserModal">
                    <team-outlined />
                    从组织架构选择
                  </a-button>
                  <div v-if="selectedRange.length > 0" class="selected-targets"><a-divider
                      orientation="left">已选择的范围</a-divider><a-tag v-for="item in selectedRange"
                      :key="`${item.type}-${item.id}`">
                      {{ item.name }} ({{ getTypeName(item.type) }})</a-tag></div>
                </div>
              </a-form-item>

              <!-- 协同管理 -->
              <div class="form-section">
                <div class="section-title">协同设置</div>
                <a-form-item label="协同编辑" name="collaborationEditType">
                  <a-radio-group v-model:value="mapForm.collaborators.editorType"
                    :options="Object.values(COLLABORATOR_TYPE).map(item => ({ label: item.name, value: item.key }))"
                    @change="mapForm.collaborators.editors = []">
                  </a-radio-group>
                  <div v-if="mapForm.collaborators.editorType === 'PART'" style="margin-top: 16px">
                    <UserSelect v-model:value="mapForm.collaborators.editors" mode="multiple" placeholder="请选择协同编辑人员"
                      style="width: 80%; margin-right: 16px" query-admin />
                    <a-button type="primary" @click="showEditorSelectionModal">
                      <template #icon><team-outlined /></template>
                      批量选择
                    </a-button>
                  </div>
                </a-form-item>
              </div>
            </a-spin>
          </a-form>
        </div>

        <!-- 底部操作按钮区域 -->
        <div class="footer-actions">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" style="margin-left: 8px" @click="handleSave" v-if="hasPermission('map:edit')"
            :loading="submitting">
            保存修改
          </a-button>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 组织架构选择弹窗 - 可见范围 -->
    <a-modal v-model:visible="orgUserModalVisible" title="选择可见范围" width="800px" :footer="null">
      <org-user-component v-if="orgUserModalVisible" selection-type="multiple" @confirm="handleOrgUserConfirm"
        @cancel="handleOrgUserCancel" :selected-ids="selectedRange" />
    </a-modal>

    <!-- 协同人员选择弹窗 - 协同编辑 -->
    <CollaboratorSelectionModal v-model:visible="editorSelectionModalVisible" @cancel="handleEditorSelectionCancel"
      @confirm="handleEditorSelectionConfirm" :preSelectedUsers="selectedEditors" />

    <!-- 添加任务弹窗 -->
    <AddTaskModal v-model:visible="taskModalVisible" @confirm="handleAddTaskConfirm" @cancel="handleAddTaskCancel" />

  </a-layout>
</template>

<script>
import { ref, reactive, computed, onMounted, defineComponent, watch } from 'vue';
import HeaderComponent from '@/components/common/HeaderComponent.vue';
import SiderComponent from '@/components/common/SiderComponent.vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  getLearningMapDetail,
  updateLearningMap
} from '@/api/learningMap';

import { hasPermission } from '@/utils/permission';
import OrgUserComponent from '@/components/common/OrgUserComponent.vue';
import CollaboratorSelectionModal from '@/components/common/CollaboratorSelectionModal.vue';
import {
  UserOutlined, SettingOutlined,
  LogoutOutlined,
  DownOutlined,
  DashboardOutlined,
  ReadOutlined,
  SolutionOutlined,
  ClusterOutlined,
  TrophyOutlined,
  AppstoreOutlined,
  PlusOutlined,
  DeleteOutlined,
  TeamOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue';
import PageBreadcrumb from '../../components/common/PageBreadcrumb.vue';
import { queryRange } from '@/api/common';
import { COLLABORATOR_TYPE, VISIBILITY_TYPE } from '@/utils/constants';
import CategoryTreeSelect from '@/components/common/CategoryTreeSelect.vue';
import ImageCropper from '@/components/common/ImageCropper.vue';
import UserSelect from '@/components/common/UserSelect.vue';
import CertificateSelect from '@/components/common/CertificateSelect.vue';
import AddTaskModal from '@/components/common/AddTaskModal.vue';
import { getUserList } from "@/api/user";
import { getUserDepartments } from "@/api/org";

export default defineComponent({
  name: 'LearningMapEditPage',
  components: {
    HeaderComponent,
    SiderComponent,
    UserOutlined,
    SettingOutlined,
    LogoutOutlined,
    DownOutlined,
    DashboardOutlined,
    ReadOutlined,
    SolutionOutlined,
    ClusterOutlined,
    TrophyOutlined,
    AppstoreOutlined,
    PlusOutlined,
    DeleteOutlined,
    TeamOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    OrgUserComponent,
    CollaboratorSelectionModal,
    CategoryTreeSelect,
    PageBreadcrumb,
    ImageCropper,
    UserSelect,
    CertificateSelect,
    AddTaskModal
  },
  setup() {
    // 初始化dayjs中文
    dayjs.locale('zh-cn');
    const router = useRouter();
    const route = useRoute();
    const mapId = ref(parseInt(route.params.id));
    const mapFormRef = ref(null);
    const collapsed = ref(false);
    const selectedKeys = ref(['mapList']);
    const openKeys = ref(['learning']);
    const loading = ref(false);
    const categoryModalVisible = ref(false);
    const submitting = ref(false);



    // 日期范围
    const dateRange = ref([]);

    // 活动的阶段key
    const activeStageKeys = ref([]);

    // 组织架构选择弹窗
    const orgUserModalVisible = ref(false);
    const editorOrgUserModalVisible = ref(false);
    const userOrgUserModalVisible = ref(false);

    // 协同人员选择弹窗
    const editorSelectionModalVisible = ref(false);
    const userSelectUserSelectionModalVisible = ref(false);

    // 添加已选协同人员详细信息
    const selectedEditors = ref([]);

    // 任务弹窗
    const taskModalVisible = ref(false);
    const currentStageIndex = ref(0);

    // 任务表格列
    const taskColumns = [
      {
        title: '任务类型',
        dataIndex: 'type',
        key: 'type',
        width: 100
      },
      {
        title: '任务内容',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true
      },
      {
        title: '是否必修',
        dataIndex: 'isRequired',
        key: 'isRequired',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 120
      }
    ];

    // 学习地图表单
    const mapForm = reactive({
      id: null,
      name: '',
      cover: '',
      introduction: '',
      creditRule: 0,
      requiredCredit: 0,
      electiveCredit: 0,
      categoryIds: '',
      certificateRule: 0,
      certificateId: null,
      dingtalkGroup: 0,
      startTime: '',
      endTime: '',
      unlockMode: 0,
      theme: 'business',
      stages: [],
      deleteStageIds: [],
      deleteTaskIds: [],
      visibility: {
        type: 'ALL',
        targets: []
      },
      collaborators: {
        editorType: 'ALL',
        editors: []
      }
    });

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入地图名称', trigger: 'blur' },
        { max: 100, message: '地图名称最多100个字符', trigger: 'blur' }
      ],
      cover: [
        { required: true, message: '请上传封面图', trigger: 'change' }
      ],
      introduction: [
        { max: 500, message: '地图简介最多500个字符', trigger: 'blur' }
      ],
      certificateId: [
        {
          required: true,
          message: '请选择证书',
          trigger: 'change',
          validator: (rule, value) => {
            if (mapForm.certificateRule === 1 && !value) {
              return Promise.reject('请选择证书');
            }
            return Promise.resolve();
          }
        }
      ]
    };

    // 选择范围
    const selectedRange = ref([]);

    // 获取用户信息
    const getUserInfo = () => {
      const userInfoStr = sessionStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          userInfo.value = JSON.parse(userInfoStr);
        } catch (error) {
          console.error('解析用户信息失败:', error);
        }
      }
    };

    // 获取学习地图详情
    const fetchLearningMapDetail = async () => {
      loading.value = true;
      try {
        const res = await getLearningMapDetail(mapId.value);
        if (res.code === 200 && res.data) {
          const mapDetail = res.data;

          // 填充表单数据
          mapForm.id = mapDetail.id;
          mapForm.name = mapDetail.name;
          mapForm.cover = mapDetail.cover;
          mapForm.introduction = mapDetail.introduction;
          mapForm.creditRule = mapDetail.creditRule;
          mapForm.requiredCredit = mapDetail.requiredCredit;
          mapForm.electiveCredit = mapDetail.electiveCredit;
          mapForm.categoryIds = mapDetail.categoryIds || '';
          mapForm.certificateRule = mapDetail.certificateRule;
          mapForm.certificateId = mapDetail.certificateId;
          mapForm.dingtalkGroup = mapDetail.dingtalkGroup;
          mapForm.unlockMode = mapDetail.unlockMode;
          mapForm.theme = mapDetail.theme || 'business';
          mapForm.collaborators = mapDetail.collaborators;
          mapForm.visibility = mapDetail.visibility;

          // 获取已选协同人员的详细信息
          await loadSelectedEditors();

          // 设置开放时间
          if (mapDetail.startTime && mapDetail.endTime) {
            mapForm.startTime = mapDetail.startTime;
            mapForm.endTime = mapDetail.endTime;
            dateRange.value = [
              dayjs(mapDetail.startTime),
              dayjs(mapDetail.endTime)
            ];
          }

          // 设置阶段
          if (mapDetail.stages && mapDetail.stages.length > 0) {
            mapForm.stages = mapDetail.stages.map(stage => {
              // 处理阶段时间
              let stageTimeRange = [];
              if (stage.startTime && stage.endTime) {
                stageTimeRange = [
                  dayjs(stage.startTime),
                  dayjs(stage.endTime)
                ];
              }

              // 处理任务
              const tasks = stage.tasks ? stage.tasks.map(task => ({
                id: task.id,
                type: task.type,
                contentId: task.contentId,
                title: task.title,
                contentType: task.contentType,
                isRequired: task.isRequired,
                sortOrder: task.sortOrder
              })) : [];

              return {
                id: stage.id,
                name: stage.name,
                stageOrder: stage.stageOrder,
                openType: stage.openType,
                startTime: stage.startTime,
                endTime: stage.endTime,
                durationDays: stage.durationDays,
                credit: stage.credit,
                certificateId: stage.certificateId,
                tasks: tasks,
                stageTimeRange,
              };
            });

            // 设置默认展开第一个阶段
            if (mapForm.stages.length > 0) {
              activeStageKeys.value = [mapForm.stages[0].id || 'new-0'];
            }
          }

          getRange();
        } else {
          message.error(res.message || '获取学习地图详情失败');
          router.push('/map/list');
        }
      } catch (error) {
        console.error('获取学习地图详情失败:', error);
        router.push('/map/list');
      } finally {
        loading.value = false;
      }
    };

    // 获取已选协同人员的详细信息
    const loadSelectedEditors = async () => {
      if (mapForm.collaborators && mapForm.collaborators.editors && mapForm.collaborators.editors.length > 0) {
        try {
          console.log('开始获取已选协同人员详细信息:', mapForm.collaborators.editors);

          const editorIds = mapForm.collaborators.editors;
          const { data: editorData } = await getUserList({ userIds: editorIds });
          const filteredEditors = editorData || [];

          // 获取部门信息
          const { data: deptData } = await getUserDepartments(editorIds);
          if (deptData && Array.isArray(deptData)) {
            // 创建用户ID到部门信息的映射
            const deptMap = new Map();
            deptData.forEach(item => {
              if (item && (item.userId || item.id)) {
                const key = item.userId || item.id;
                deptMap.set(key, item);
              }
            });

            // 更新用户的部门信息
            filteredEditors.forEach(user => {
              const userId = user.userId || user.id;
              const deptInfo = deptMap.get(userId);
              if (deptInfo) {
                user.departmentId = deptInfo.departmentId;
                user.departmentName = deptInfo.departmentName;
                user.departments = deptInfo.departments || [];
                user.computedDepartment = deptInfo.departments?.map(d => d.name).join(',') || deptInfo.departmentName || '未分配';
              }
            });
          }
          console.log('获取到的协同人员基本信息:', filteredEditors);

          // 获取部门信息
          if (filteredEditors.length > 0) {
            const userIds = filteredEditors.map(user => user.userId || user.id).filter(Boolean);

            try {
              const { getUserDepartments } = await import('@/api/org');
              const deptResponse = await getUserDepartments(userIds);

              if (deptResponse && deptResponse.data) {
                let deptData;
                if (deptResponse.data.data && Array.isArray(deptResponse.data.data)) {
                  deptData = deptResponse.data.data;
                } else if (Array.isArray(deptResponse.data)) {
                  deptData = deptResponse.data;
                }

                if (deptData) {
                  // 创建用户ID到部门信息的映射
                  const deptMap = new Map();
                  deptData.forEach(item => {
                    if (item && (item.userId || item.id)) {
                      const key = item.userId || item.id;
                      deptMap.set(key, item);
                    }
                  });

                  // 更新用户的部门信息
                  filteredEditors.forEach(user => {
                    const userId = user.userId || user.id;
                    const deptInfo = deptMap.get(userId);

                    if (deptInfo) {
                      if (deptInfo.departmentId) {
                        user.departmentId = deptInfo.departmentId;
                      }
                      if (deptInfo.departmentName) {
                        user.departmentName = deptInfo.departmentName;
                      }
                      if (deptInfo.departments && Array.isArray(deptInfo.departments)) {
                        user.departments = [...deptInfo.departments];
                        user.computedDepartment = deptInfo.departments.map(d => d.name).join(',');
                      } else if (deptInfo.departmentName) {
                        user.computedDepartment = deptInfo.departmentName;
                      }
                    }
                  });
                }
              }
            } catch (deptError) {
              console.error('获取协同人员部门信息失败:', deptError);
            }
          }

          // 标准化用户数据格式
          selectedEditors.value = filteredEditors.map(user => ({
            id: user.userId || user.id,
            userId: user.userId || user.id,
            username: user.username || user.employeeNo || '',
            nickname: user.nickname || user.username || '',
            avatar: user.avatar || user.avatarUrl || '',
            department: user.computedDepartment || user.departmentName || '未分配',
            departmentId: user.departmentId || '',
            departments: user.departments || [],
            computedDepartment: user.computedDepartment || user.departmentName || '未分配'
          }));

          console.log('已选协同人员详细信息获取完成:', selectedEditors.value);
        } catch (error) {
          console.error('获取协同人员详细信息失败:', error);
          selectedEditors.value = [];
        }
      } else {
        selectedEditors.value = [];
      }
    };



    // 处理日期范围变更
    const handleDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        mapForm.startTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
        mapForm.endTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
      } else {
        mapForm.startTime = '';
        mapForm.endTime = '';
      }
    };

    // 处理阶段时间变更
    const handleStageTimeChange = (dates, stageIndex) => {
      if (dates && dates.length === 2) {
        mapForm.stages[stageIndex].startTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
        mapForm.stages[stageIndex].endTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
      } else {
        mapForm.stages[stageIndex].startTime = '';
        mapForm.stages[stageIndex].endTime = '';
      }
    };

    // 获取阶段标题
    const getStageHeader = (stage, index) => {
      return `${index + 1}. ${stage.name || '未命名阶段'}`;
    };

    // 添加阶段
    const handleAddStage = () => {
      const newStage = {
        key: `new-${Date.now()}`,
        name: `阶段${mapForm.stages.length + 1}`,
        stageOrder: mapForm.stages.length + 1,
        openType: 0,
        startTime: '',
        endTime: '',
        durationDays: 7,
        credit: 0,
        certificateId: null,
        tasks: []
      };

      mapForm.stages.push(newStage);
      activeStageKeys.value = [newStage.key];
    };

    // 删除阶段
    const handleDeleteStage = (index) => {
      const stage = mapForm.stages[index];
      // 如果是已有阶段，添加到删除列表
      if (stage.id) {
        mapForm.deleteStageIds.push(stage.id);
        // 添加阶段下的任务到删除列表
        if (stage.tasks && stage.tasks.length > 0) {
          stage.tasks.forEach(task => {
            if (task.id) {
              mapForm.deleteTaskIds.push(task.id);
            }
          });
        }
      }

      // 从阶段列表中移除
      mapForm.stages.splice(index, 1);
    };

    // 添加任务
    const handleAddTask = (stageIndex) => {
      currentStageIndex.value = stageIndex;
      taskModalVisible.value = true;
    };

    // 处理添加任务确认
    const handleAddTaskConfirm = (selectedTasks) => {
      if (selectedTasks && selectedTasks.length > 0) {
        const targetStage = mapForm.stages[currentStageIndex.value];
        // 将选中的任务添加到当前阶段
        const tasks = selectedTasks.map((task, index) => ({
          key: `new-${Date.now()}-${index}`,
          type: task.type,
          contentId: task.id,
          title: task.title,
          isRequired: task.isRequired,
          sortOrder: targetStage.tasks.length + index + 1
        })).filter(item => !targetStage.tasks.find(task => task.contentId === item.contentId));

        targetStage.tasks.push(...tasks);
      }
      taskModalVisible.value = false;
    };

    // 处理添加任务取消
    const handleAddTaskCancel = () => {
      taskModalVisible.value = false;
    };

    // 移动任务
    const moveTask = (stageIndex, taskIndex, direction) => {
      const tasks = mapForm.stages[stageIndex].tasks;
      const newIndex = taskIndex + direction;
      if (newIndex < 0 || newIndex >= tasks.length) {
        return;
      }
      const temp = tasks[taskIndex];
      tasks[taskIndex] = tasks[newIndex];
      tasks[newIndex] = temp;

      // 更新顺序
      tasks.forEach((task, idx) => {
        task.sortOrder = idx;
      });
    };

    // 删除任务
    const handleDeleteTask = (stageIndex, taskIndex) => {
      const task = mapForm.stages[stageIndex].tasks[taskIndex];

      // 如果是已有任务，添加到删除列表
      if (task.id) {
        mapForm.deleteTaskIds.push(task.id);
      }

      // 从任务列表中移除
      mapForm.stages[stageIndex].tasks.splice(taskIndex, 1);
    };

    // 获取任务类型名称
    const getTaskTypeName = (type) => {
      const typeMap = {
        'COURSE': '课程',
        'TRAIN': '培训',
        'EXAM': '考试',
        'ASSIGNMENT': '作业',
        'SURVEY': '调研'
      };
      return typeMap[type] || type;
    };

    /** 获取范围回显 */
    const getRange = () => {
      queryRange({
        businessType: 'LEARNING_MAP',
        businessId: mapForm.id,
        functionType: 'visibility'
      }).then(res => {
        console.log(res, '范围配置')

        const tempArray = [];
        // 处理targets字段，将后端的TargetsDTO格式转换为前端期望的List<TargetDTO>格式
        const targets = [];
        const { departmentInfos, roleInfos, userInfos } = res.data;
        departmentInfos.forEach(item => {
          tempArray.push({ id: item.departmentId, type: 'department', name: item.departmentName });
        })
        roleInfos.forEach(item => {
          tempArray.push({ id: item.roleId, type: 'role', name: item.roleName });
        })
        userInfos.forEach(item => {
          tempArray.push({ id: item.userId, type: 'user', name: item.userName });
        })
        // 处理targets字段，将后端的TargetsDTO格式转换为前端期望的List<TargetDTO>格式
        targets.push({
          type: 'department',
          ids: departmentInfos.map(item => item.departmentId)
        })
        targets.push({
          type: 'role',
          ids: roleInfos.map(item => item.roleId)
        })
        targets.push({
          type: 'user',
          ids: userInfos.map(item => item.userId)
        })
        // 更新表单数据
        mapForm.visibility.targets = targets;
        selectedRange.value = tempArray;
      });
    };

    // 显示组织架构选择弹窗 - 可见范围
    const showOrgUserModal = () => {
      orgUserModalVisible.value = true;
    };

    // 处理组织架构选择确认- 可见范围
    const handleOrgUserConfirm = (result) => {
      // 处理选择结果
      const tempArray = [];
      const targets = [];

      // 添加部门目标
      if (result.departments && result.departments.length > 0) {
        targets.push({
          type: 'department',
          ids: result.departments.map(dept => dept.id)
        });

        result.departments.forEach(item => {
          tempArray.push({ id: item.id, type: 'department', name: item.name });
        });
      }

      // 添加角色目标
      if (result.roles && result.roles.length > 0) {
        targets.push({
          type: 'role',
          ids: result.roles.map(role => role.id)
        });

        result.roles.forEach(item => {
          tempArray.push({ id: item.id, type: 'role', name: item.name });
        });
      }

      // 添加用户目标
      if (result.users && result.users.length > 0) {
        targets.push({
          type: 'user',
          ids: result.users.map(user => user.id)
        });

        result.users.forEach(item => {
          tempArray.push({ id: item.id, type: 'user', name: item.name });
        });
      }

      // 更新表单数据
      mapForm.visibility.targets = targets;

      // 更新选择范围
      selectedRange.value = tempArray;

      // 关闭弹窗
      orgUserModalVisible.value = false;
    };

    // 处理组织架构选择取消 - 可见范围
    const handleOrgUserCancel = () => {
      orgUserModalVisible.value = false;
    };

    // 显示协同编辑人员选择弹窗
    const showEditorSelectionModal = () => {
      editorSelectionModalVisible.value = true;
    };

    // 处理协同编辑人员选择确认
    const handleEditorSelectionConfirm = (selectedUsers) => {
      console.log('协同编辑人员选择确认:', selectedUsers);
      mapForm.collaborators.editors = selectedUsers.map(user => user.id);
      // 更新已选协同人员详细信息
      selectedEditors.value = selectedUsers;
      editorSelectionModalVisible.value = false;
    };

    // 处理协同编辑人员选择取消
    const handleEditorSelectionCancel = () => {
      editorSelectionModalVisible.value = false;
    };

    // 是否有已选择的可见范围目标
    const hasSelectedVisibilityTargets = computed(() => {
      return selectedRange.value.length > 0;
    });

    // 获取类型名称
    const getTypeName = (type) => {
      const typeMap = {
        'department': '部门',
        'role': '角色',
        'user': '人员'
      };
      return typeMap[type] || type;
    };

    // 过滤内容选项
    const filterContentOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 处理保存
    const handleSave = () => {
      mapFormRef.value.validate().then(() => {

        // 提交表单
        submitForm();
      }).catch(error => {
        console.log('表单验证失败:', error);
      });
    };

    // 提交表单
    const submitForm = async () => {
      submitting.value = true;
      try {
        const res = await updateLearningMap(mapForm);
        if (res.code === 200) {
          message.success('学习地图更新成功');
          // 跳转到学习地图列表页
          router.push('/map/list');
        } else {
          message.error(res.message || '更新失败');
        }
      } catch (error) {
        console.error('更新学习地图失败:', error);
      } finally {
        submitting.value = false;
      }
    };

    // 处理取消
    const handleCancel = () => {
      // 跳转到学习地图列表页
      router.push('/map/list');
    };

    // 退出登录
    const handleLogout = () => {
      // 清除登录信息
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('userInfo');
      localStorage.removeItem('rememberMe');

      message.success('已退出登录');

      // 跳转到登录页
      router.push('/login');
    };

    // 页面跳转
    const navigateTo = (path) => {
      router.push(path);
    };

    // 显示类目管理弹窗
    const showCategoryModal = () => {
      categoryModalVisible.value = true;
    };

    // 处理裁剪成功
    const handleCropSuccess = (imageUrl, data) => {
      console.log('裁剪成功:', imageUrl, data);
      // 封面图片URL已通过v-model自动更新到mapForm.cover
      mapFormRef.value.validateFields(['cover']);
    };

    // 禁用日期
    const disabledDate = (current) => {
      return current && current < dayjs().startOf('day');
    };

    onMounted(() => {
      // 获取学习地图详情
      fetchLearningMapDetail();
    });

    return {
      mapId,
      mapFormRef,
      collapsed,
      selectedKeys,
      openKeys,
      loading,
      dateRange,
      activeStageKeys,
      orgUserModalVisible,
      editorOrgUserModalVisible,
      userOrgUserModalVisible,
      editorSelectionModalVisible,
      userSelectUserSelectionModalVisible,
      taskModalVisible,
      currentStageIndex,
      taskColumns,
      mapForm,
      rules,
      categoryModalVisible,
      selectedRange,
      hasSelectedVisibilityTargets,
      hasPermission,
      selectedEditors,
      loadSelectedEditors,
      handleDateRangeChange,
      handleStageTimeChange,
      getStageHeader,
      handleAddStage,
      handleDeleteStage,
      handleAddTask,
      handleAddTaskConfirm,
      handleAddTaskCancel,
      moveTask,
      handleDeleteTask,
      getTaskTypeName,
      showOrgUserModal,
      handleOrgUserConfirm,
      handleOrgUserCancel,
      showEditorSelectionModal,
      handleEditorSelectionConfirm,
      handleEditorSelectionCancel,
      getTypeName,
      filterContentOption,
      handleSave,
      handleCancel,
      handleLogout,
      navigateTo,
      showCategoryModal,
      handleCropSuccess,
      COLLABORATOR_TYPE,
      VISIBILITY_TYPE,
      submitting,
      dayjs,
      disabledDate
    };
  }
});
</script>

<style scoped>
.learning-map-edit-layout {
  min-height: 100vh;
}

.content {
  position: relative;
  padding: 24px 24px 80px;
  background-color: #f0f2f5;
  min-height: 280px;
  overflow: auto;
}

.content-header {
  padding-bottom: 12px;
}

.form-container {
  height: calc(100% - 36px);
  overflow-y: auto;
  background-color: #FFFFFF;
  padding: 24px;
  border-radius: 4px;
}

.form-container .form {
  height: 100%;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.sticky-title {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 100;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.upload-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
}

.empty-stages,
.empty-tasks {
  padding: 24px;
  text-align: center;
}

.stages-container {
  margin-bottom: 24px;
}

.tasks-section {
  margin-top: 24px;
}

.selected-targets {
  margin-top: 16px;
}

.footer-actions {
  text-align: left;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: calc(100% - 48px);
  padding: 24px;
}
</style>
