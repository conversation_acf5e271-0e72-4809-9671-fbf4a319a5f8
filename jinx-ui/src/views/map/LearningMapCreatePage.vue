<template>
  <a-layout class="learning-map-create-layout">
    <!-- 使用 HeaderComponent -->
    <HeaderComponent activeKey="MANAGEMENT_CENTER" :showSearch="false" />

    <a-layout>
      <!-- 使用 SiderComponent -->
      <SiderComponent v-model:collapsed="collapsed" v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys"
        @category-modal-show="showCategoryModal" />

      <!-- 内容区 -->
      <a-layout-content class="content">
        <div class="content-header">
          <PageBreadcrumb />
        </div>

        <!-- 表单区域 -->
        <div class="form-container">
          <a-form ref="formRef" :model="formData" class="form" :rules="rules" :label-col="{ style: { width: '120px' } }"
            :wrapper-col="{ span: 12 }">
            <!-- 基本信息 -->
            <div class="section-title">基本信息</div>
            <a-form-item label="地图名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入地图名称" />
            </a-form-item>
            <a-form-item label="分类" name="categoryIds">
              <CategoryTreeSelect v-model:value="formData.categoryIds" />
            </a-form-item>
            <a-form-item label="封面图" name="cover">
              <ImageCropper v-model:value="formData.cover" :width="640" :height="360" label="选择图片"
                tip="支持JPG、PNG格式，裁剪为 640x360 像素" extra="请上传清晰美观的封面图，作为学习地图的主要视觉元素" @crop-success="handleCropSuccess" />
            </a-form-item>


            <a-form-item label="地图简介" name="introduction">
              <a-textarea v-model:value="formData.introduction" placeholder="请输入地图简介" :rows="4" />
            </a-form-item>

            <a-form-item label="学分规则" name="creditRule">
              <a-radio-group v-model:value="formData.creditRule">
                <a-radio :value="0">整体发放</a-radio>
                <a-radio :value="1">按阶段发放</a-radio>
              </a-radio-group>
            </a-form-item>
            <template v-if="formData.creditRule === 0">
              <a-form-item label="学分" name="requiredCredit">
                <a-input-number v-model:value="formData.requiredCredit" :min="0" :precision="1" style="width: 100%" />
              </a-form-item>
              <!-- <a-form-item label="选修学分" name="electiveCredit"><a-input-number v-model:value="formData.electiveCredit"
                  :min="0" :precision="1" style="width: 100%" />
              </a-form-item> -->
            </template>
            <a-form-item label="证书规则" name="certificateRule"><a-radio-group v-model:value="formData.certificateRule">
                <a-radio :value="0">不发放</a-radio>
                <a-radio :value="1">整体发放</a-radio><a-radio :value="2">按阶段发放</a-radio></a-radio-group>
            </a-form-item>
            <template v-if="formData.certificateRule === 1">
              <a-form-item label="证书" name="certificateId">
                <CertificateSelect v-model:value="formData.certificateId" />
              </a-form-item>
            </template>


            <!-- 学习规则 -->
            <div class="section-title">学习规则</div>
            <a-form-item label="开放时间" name="openTime">
              <a-range-picker v-model:value="dateRange" style="width: 100%" @change="handleDateRangeChange"
                format="YYYY-MM-DD HH:mm:ss" :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                }" :disabledDate="disabledDate" />
            </a-form-item>
            <a-form-item label="解锁方式" name="unlockMode">
              <a-radio-group v-model:value="formData.unlockMode"><a-radio :value="0">按阶段和任务</a-radio>
                <a-radio :value="1">按阶段</a-radio><a-radio :value="2">自由模式</a-radio>
              </a-radio-group></a-form-item>



            <!-- <a-form-item label="主题皮肤" name="theme">
              <a-radio-group v-model:value="formData.theme" button-style="solid"><a-radio-button
                  value="business">商务简约</a-radio-button><a-radio-button value="tech">动感科技</a-radio-button>
                <a-radio-button value="farm">农场时光</a-radio-button><a-radio-button
                  value="chinese">中国元素</a-radio-button><a-radio-button
                  value="list">列表模式</a-radio-button></a-radio-group>
            </a-form-item> -->
            <!-- <a-form-item label="钉钉群" name="dingtalkGroup">
              <a-radio-group v-model:value="formData.dingtalkGroup"><a-radio :value="0">不创建</a-radio>
                <a-radio :value="1">创建钉钉群</a-radio>
              </a-radio-group>
            </a-form-item> -->

            <!-- 阶段管理 -->

            <!-- 阶段管理 -->
            <div class="section-title sticky-title">
              阶段管理
              <a-button type="primary" size="small" style="margin-left: 16px;" @click="addStage">
                <plus-outlined />添加阶段
              </a-button>
            </div>

            <div class="stage-list">
              <div v-if="formData.stages.length === 0" class="empty-stage">
                <a-empty description="暂无阶段，请添加阶段" />
              </div>
              <div v-else>
                <a-collapse v-model:activeKey="activeStageKeys"><a-collapse-panel
                    v-for="(stage, stageIndex) in formData.stages" :key="stageIndex"
                    :header="stage.name || `阶段${stageIndex + 1}`">
                    <template #extra>
                      <a-space>
                        <a-button type="text" @click.stop="moveStage(stageIndex, -1)" :disabled="stageIndex === 0">
                          <arrow-up-outlined /></a-button><a-button type="text" @click.stop="moveStage(stageIndex, 1)"
                          :disabled="stageIndex === formData.stages.length - 1">
                          <arrow-down-outlined /></a-button><a-button type="text" danger
                          @click.stop="removeStage(stageIndex)">
                          <delete-outlined /></a-button></a-space>
                    </template>

                    <!-- 阶段表单 -->
                    <a-form>
                      <a-row :gutter="16">
                        <a-col :span="12">
                          <a-form-item label="阶段名称" required>
                            <a-input v-model:value="stage.name" placeholder="请输入阶段名称" />
                          </a-form-item></a-col><a-col :span="12"><a-form-item label="开放类型" required><a-radio-group
                              v-model:value="stage.openType"><a-radio :value="0">不设置</a-radio>
                              <a-radio :value="1">固定时间</a-radio>
                              <a-radio :value="2">学习期限</a-radio>
                            </a-radio-group></a-form-item>
                        </a-col>
                      </a-row><a-row :gutter="16" v-if="stage.openType === 1"><a-col :span="24">
                          <a-form-item label="开放时间" required>
                            <a-range-picker v-model:value="stage.dateRange" style="width: 100%"
                              @change="(dates) => handleStageTimeChange(stageIndex, dates)" format="YYYY-MM-DD HH:mm:ss"
                              :show-time="{
                                hideDisabledOptions: true,
                                defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                              }" :disabledDate="disabledDate" />
                          </a-form-item></a-col></a-row><a-row :gutter="16" v-if="stage.openType === 2"><a-col
                          :span="12">
                          <a-form-item label="学习期限(天)" required><a-input-number v-model:value="stage.durationDays"
                              :min="1" style="width: 100%" />
                          </a-form-item></a-col></a-row>

                      <a-row :gutter="16" v-if="formData.creditRule === 1">
                        <a-col :span="12">
                          <a-form-item label="阶段学分" required><a-input-number v-model:value="stage.credit" :min="0"
                              :precision="1" style="width: 100%" /></a-form-item>
                        </a-col>
                      </a-row>

                      <a-row :gutter="16" v-if="formData.certificateRule === 2">
                        <a-form-item label="阶段证书" required>
                          <CertificateSelect v-model:value="stage.certificateId" />
                        </a-form-item>
                      </a-row>

                      <!-- 任务管理 -->
                      <div class="task-management">
                        <div class="task-header">
                          <h4>任务管理</h4>
                          <a-button type="primary" size="small" @click="showAddTaskModal(stageIndex)">
                            <plus-outlined />添加任务</a-button>
                        </div>

                        <div v-if="stage.tasks && stage.tasks.length > 0" class="task-list">
                          <a-table :dataSource="stage.tasks" :columns="taskColumns" :pagination="false" size="small">
                            <template #bodyCell="{ column, record, index }">
                              <template v-if="column.key === 'type'">
                                <a-tag :color="getTaskTypeColor(record.type)">
                                  {{ getTaskTypeName(record.type) }}</a-tag>
                              </template>
                              <template v-if="column.key === 'isRequired'">
                                <a-switch v-model:checked="record.isRequired" :checkedValue="true"
                                  :unCheckedValue="false" size="small" /></template>
                              <template v-if="column.key === 'action'">
                                <a-space>
                                  <a-button type="text" size="small" @click="moveTask(stageIndex, index, -1)"
                                    :disabled="index === 0">
                                    <arrow-up-outlined /></a-button>
                                  <a-button type="text" size="small" @click="moveTask(stageIndex, index, 1)"
                                    :disabled="index === stage.tasks.length - 1">
                                    <arrow-down-outlined /></a-button>
                                  <a-button type="text" dangersize="small" @click="removeTask(stageIndex, index)">
                                    <delete-outlined />
                                  </a-button></a-space>
                              </template>
                            </template></a-table>
                        </div>
                        <div v-else class="empty-task"><a-empty description="暂无任务，请添加任务" />
                        </div>
                      </div>
                    </a-form></a-collapse-panel>
                </a-collapse>
              </div>
            </div>

            <!-- 可见范围设置 -->
            <div class="section-title">可见范围设置</div>
            <a-form-item name="visibility.type" label="可见范围">
              <a-radio-group v-model:value="formData.visibility.type"
                :options="Object.values(VISIBILITY_TYPE).map(item => ({ label: item.name, value: item.key }))"
                @change="formData.visibility.targets = []">
              </a-radio-group>
              <div v-if="formData.visibility.type === 'PART'" style="margin-top: 16px">
                <a-button type="primary" @click="showOrgUserModal('visibility')">
                  <team-outlined />
                  从组织架构选择
                </a-button>
                <div v-if="selectedTargets.length > 0" class="selected-targets">
                  <a-divider orientation="left">已选择的范围</a-divider>
                  <a-tag v-for="target in selectedTargets" :key="`${target.type}-${target.id}`">
                    {{ target.name }} ({{ getTargetTypeName(target.type) }})
                  </a-tag>
                </div>
              </div>
            </a-form-item>



            <!-- 协同管理 -->
            <div class="form-section">
              <div class="section-title">协同设置</div>
              <a-form-item label="协同编辑" name="collaborationEditType">
                <a-radio-group v-model:value="formData.collaborators.editorType"
                  :options="Object.values(COLLABORATOR_TYPE).map(item => ({ label: item.name, value: item.key }))"
                  @change="formData.collaborators.editors = []">
                </a-radio-group>
                <div v-if="formData.collaborators.editorType === 'PART'" style="margin-top: 16px">
                  <UserSelect v-model:value="formData.collaborators.editors" mode="multiple" placeholder="请选择协同编辑人员"
                    style="width: 80%; margin-right: 16px" query-admin />
                  <a-button type="primary" @click="showEditorSelectionModal">
                    <template #icon><team-outlined /></template>
                    批量选择
                  </a-button>
                </div>
              </a-form-item>
            </div>

            <!-- 协同人员选择弹窗 - 协同编辑 -->
            <CollaboratorSelectionModal v-model:visible="editorSelectionModalVisible"
              @confirm="handleEditorSelectionConfirm" @cancel="handleEditorSelectionCancel" />

          </a-form>
        </div>
        <!-- 底部操作按钮区域 -->
        <div class="footer-actions">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitting">保存</a-button>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 添加任务弹窗 -->
    <AddTaskModal v-model:visible="taskModalVisible" @confirm="handleAddTaskConfirm" @cancel="handleAddTaskCancel" />

    <!-- 组织架构选择弹窗 -->
    <a-modal v-model:visible="orgUserModalVisible" title="选择范围" width="800px" :footer="null" :maskClosable="false"
      destroyOnClose>
      <OrgUserComponent v-if="orgUserModalVisible" :selection-type="'multiple'" :selected-ids="selectedTargets"
        @confirm="handleOrgUserConfirm" @cancel="() => orgUserModalVisible = false" />
    </a-modal>
  </a-layout>
</template>

<script>
import { ref, reactive, computed, onMounted, defineComponent, watch } from 'vue';
import HeaderComponent from '@/components/common/HeaderComponent.vue';
import SiderComponent from '@/components/common/SiderComponent.vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  createLearningMap
} from '@/api/learningMap';

import { hasPermission } from '@/utils/permission';
import OrgUserComponent from '@/components/common/OrgUserComponent.vue';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
  DashboardOutlined,
  ReadOutlined,
  SolutionOutlined,
  ClusterOutlined,
  TrophyOutlined,
  AppstoreOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  TeamOutlined
} from '@ant-design/icons-vue';
import PageBreadcrumb from '../../components/common/PageBreadcrumb.vue';
import CollaboratorSelectionModal from '@/components/common/CollaboratorSelectionModal.vue';
import { COLLABORATOR_TYPE, VISIBILITY_TYPE } from '@/utils/constants';
import CategoryTreeSelect from '@/components/common/CategoryTreeSelect.vue';
import ImageCropper from '@/components/common/ImageCropper.vue';
import UserSelect from '@/components/common/UserSelect.vue';
import CertificateSelect from '@/components/common/CertificateSelect.vue';
import AddTaskModal from '@/components/common/AddTaskModal.vue';

export default defineComponent({
  name: 'LearningMapCreatePage',
  components: {
    HeaderComponent,
    SiderComponent,
    UserOutlined,
    SettingOutlined,
    LogoutOutlined,
    DownOutlined,
    DashboardOutlined,
    ReadOutlined,
    SolutionOutlined,
    ClusterOutlined,
    TrophyOutlined,
    AppstoreOutlined,
    PlusOutlined,
    DeleteOutlined,
    EditOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    TeamOutlined,
    OrgUserComponent,
    PageBreadcrumb,
    CollaboratorSelectionModal,
    CategoryTreeSelect,
    ImageCropper,
    UserSelect,
    CertificateSelect,
    AddTaskModal
  },
  setup() {
    // 初始化dayjs中文
    dayjs.locale('zh-cn');

    const router = useRouter();
    const formRef = ref(null);
    const collapsed = ref(false);
    const selectedKeys = ref(['mapList']);
    const openKeys = ref(['learning']);
    const categoryModalVisible = ref(false);
    const submitting = ref(false);



    // 日期范围
    const dateRange = ref([]);

    // 表单数据
    const formData = reactive({
      name: '',
      cover: '',
      introduction: '',
      creditRule: 0,
      requiredCredit: 0,
      electiveCredit: 0,
      categoryIds: '',
      certificateRule: 0,
      certificateId: null,
      dingtalkGroup: 0,
      startTime: '',
      endTime: '',
      unlockMode: 0,
      theme: 'business',
      stages: [],
      visibility: {
        type: 'ALL',
        targets: []
      },
      collaborators: {
        editorType: 'ALL',
        editors: []
      }
    });

    // 表单验证规则
    const rules = {
      name: [{ required: true, message: '请输入地图名称', trigger: 'blur' }],
      cover: [
        { required: true, message: '请上传封面图', trigger: 'change' }
      ],
      categoryIds: [{ required: true, message: '请选择分类', trigger: 'change' }],
      certificateId: [{
        required: true,
        message: '请选择证书',
        trigger: 'change',
        validator: (rule, value) => {
          if (formData.certificateRule === 1 && !value) {
            return Promise.reject(rule.message);
          }
          return Promise.resolve();
        }
      }]
    };

    //阶段展开的key
    const activeStageKeys = ref([]);

    // 任务表格列
    const taskColumns = [
      {
        title: '任务类型',
        dataIndex: 'type',
        key: 'type',
        width: 100
      },
      {
        title: '任务名称',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true
      },
      {
        title: '必修',
        dataIndex: 'isRequired',
        key: 'isRequired',
        width: 80
      },
      {
        title: '操作',
        key: 'action',
        width: 120
      }
    ];

    // 任务弹窗
    const taskModalVisible = ref(false);
    const currentStageIndex = ref(-1);

    // 显示添加任务弹窗
    const showAddTaskModal = (stageIndex) => {
      currentStageIndex.value = stageIndex;
      taskModalVisible.value = true;
    };

    // 处理添加任务确认
    const handleAddTaskConfirm = (selectedTasks) => {
      if (selectedTasks && selectedTasks.length > 0) {
        const targetStage = formData.stages[currentStageIndex.value];
        // 将选中的任务添加到当前阶段
        const tasks = selectedTasks.map((task, index) => ({
          key: `new-${Date.now()}-${index}`,
          type: task.type,
          contentId: task.id,
          title: task.title,
          isRequired: task.isRequired,
          sortOrder: targetStage.tasks.length + index + 1
        })).filter(item => !targetStage.tasks.find(task => task.contentId === item.contentId));

        targetStage.tasks.push(...tasks);
      }
      taskModalVisible.value = false;
    };

    // 处理添加任务取消
    const handleAddTaskCancel = () => {
      taskModalVisible.value = false;
    };

    // 内容列表
    const contentList = ref([]);
    const contentLoading = ref(false);
    // 组织架构选择弹窗
    const orgUserModalVisible = ref(false);
    // 已选择的目标
    const selectedTargets = ref([]);
    const selectedEditors = ref([]);
    const selectedUsers = ref([]);

    // 协同编辑弹窗
    const editorSelectionModalVisible = ref(false);
    const userSelectionModalVisible = ref(false);



    // 处理日期范围变更
    const handleDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        formData.startTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
        formData.endTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
      } else {
        formData.startTime = '';
        formData.endTime = '';
      }
    };

    // 处理阶段时间变更
    const handleStageTimeChange = (stageIndex, dates) => {
      if (dates && dates.length === 2) {
        formData.stages[stageIndex].startTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
        formData.stages[stageIndex].endTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
      } else {
        formData.stages[stageIndex].startTime = '';
        formData.stages[stageIndex].endTime = '';
      }
    };

    // 添加阶段
    const addStage = () => {
      const newStage = {
        name: `阶段${formData.stages.length + 1}`,
        stageOrder: formData.stages.length,
        openType: 0,
        startTime: '',
        endTime: '',
        durationDays: 7,
        credit: 0,
        certificateId: null,
        dateRange: [],
        tasks: []
      };
      formData.stages.push(newStage);
      activeStageKeys.value.push(formData.stages.length - 1);
    };// 移动阶段
    const moveStage = (index, direction) => {
      const newIndex = index + direction;
      if (newIndex < 0 || newIndex >= formData.stages.length) {
        return;
      }
      const temp = formData.stages[index];
      formData.stages[index] = formData.stages[newIndex];
      formData.stages[newIndex] = temp;
      // 更新顺序
      formData.stages.forEach((stage, idx) => {
        stage.stageOrder = idx;
      });
      // 更新展开的key
      const activeKeys = [...activeStageKeys.value];
      const indexOfCurrent = activeKeys.indexOf(index);
      const indexOfTarget = activeKeys.indexOf(newIndex);

      if (indexOfCurrent !== -1) {
        activeKeys[indexOfCurrent] = newIndex;
      }
      if (indexOfTarget !== -1) {
        activeKeys[indexOfTarget] = index;
      }
      activeStageKeys.value = activeKeys;
    };

    // 删除阶段
    const removeStage = (index) => {
      formData.stages.splice(index, 1);
      // 更新顺序
      formData.stages.forEach((stage, idx) => {
        stage.stageOrder = idx;
      });// 更新展开的key
      const activeKeys = activeStageKeys.value.filter(key => key !== index).map(key => key > index ? key - 1 : key);
      activeStageKeys.value = activeKeys;
    };

    // 显示组织架构选择弹窗
    const showOrgUserModal = (type) => {

      orgUserModalVisible.value = true;
    };

    // 处理组织架构选择确认
    const handleOrgUserConfirm = (result) => {
      const tempArray = [];
      const targets = [];

      // 处理部门
      if (result.departments && result.departments.length > 0) {
        targets.push({
          type: 'department',
          ids: result.departments.map(dept => dept.id)
        });

        result.departments.forEach(item => {
          tempArray.push({ id: item.id, type: 'department', name: item.name });
        });
      }

      // 处理角色
      if (result.roles && result.roles.length > 0) {
        targets.push({
          type: 'role',
          ids: result.roles.map(role => role.id)
        });

        result.roles.forEach(item => {
          tempArray.push({ id: item.id, type: 'role', name: item.name });
        });
      }

      // 处理用户
      if (result.users && result.users.length > 0) {
        targets.push({
          type: 'user',
          ids: result.users.map(user => user.id)
        });

        result.users.forEach(item => {
          tempArray.push({ id: item.id, type: 'user', name: item.name });
        });
      }

      // 更新表单数据
      formData.visibility.targets = targets;

      // 更新选择范围
      selectedTargets.value = tempArray;

      orgUserModalVisible.value = false;
    };


    // 移除编辑者
    const removeEditor = (index) => {
      selectedEditors.value.splice(index, 1);

      // 更新表单数据
      formData.collaborators.editors = selectedEditors.value.map(editor => editor.id);
    };

    // 获取目标类型名称
    const getTargetTypeName = (type) => {
      const typeMap = {
        'department': '部门',
        'role': '角色',
        'user': '人员'
      };
      return typeMap[type] || type;
    };

    // 处理提交
    const handleSubmit = async () => {
      try {
        await formRef.value.validate();

        submitting.value = true;


        // 发送请求
        const res = await createLearningMap(formData);

        if (res.code === 200) {
          message.success('创建学习地图成功');
          router.push('/map/list');
        } else {
          message.error(res.message || '创建学习地图失败');
        }
      } catch (error) {
        console.error('创建学习地图失败:', error);
      } finally {
        submitting.value = false;
      }
    };

    // 处理取消
    const handleCancel = () => {
      router.push('/map/list');
    };

    // 退出登录
    const handleLogout = () => {
      // 清除登录信息
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('userInfo');
      localStorage.removeItem('rememberMe');

      message.success('已退出登录');

      // 跳转到登录页
      router.push('/login');
    };

    // 页面跳转
    const navigateTo = (path) => {
      router.push(path);
    };

    // 显示类目管理弹窗
    const showCategoryModal = () => {
      categoryModalVisible.value = true;
    };

    // 显示协同编辑人员选择弹窗
    const showEditorSelectionModal = () => {
      editorSelectionModalVisible.value = true;
    };

    // 处理协同编辑人员选择确认
    const handleEditorSelectionConfirm = (selectedUsers) => {
      if (selectedUsers && selectedUsers.length > 0) {
        // 更新选中的编辑者
        selectedEditors.value = selectedUsers.map(user => ({
          id: user.id,
          name: user.name
        }));
        // 更新表单数据
        formData.collaborators.editors = selectedUsers.map(user => user.id);
      }
      editorSelectionModalVisible.value = false;
    };

    // 处理协同编辑人员选择取消
    const handleEditorSelectionCancel = () => {
      editorSelectionModalVisible.value = false;
    };

    // 处理裁剪成功
    const handleCropSuccess = (imageUrl, data) => {
      console.log('裁剪成功:', imageUrl, data);
      // 封面图片URL已通过v-model自动更新到formData.cover
      formRef.value.validateFields(['cover']);
    };

    // 移动任务
    const moveTask = (stageIndex, taskIndex, direction) => {
      const tasks = formData.stages[stageIndex].tasks;
      const newIndex = taskIndex + direction;
      if (newIndex < 0 || newIndex >= tasks.length) {
        return;
      }
      const temp = tasks[taskIndex];
      tasks[taskIndex] = tasks[newIndex];
      tasks[newIndex] = temp;

      // 更新顺序
      tasks.forEach((task, idx) => {
        task.sortOrder = idx;
      });
    };

    // 删除任务
    const removeTask = (stageIndex, taskIndex) => {
      formData.stages[stageIndex].tasks.splice(taskIndex, 1);

      // 更新顺序
      formData.stages[stageIndex].tasks.forEach((task, idx) => {
        task.sortOrder = idx;
      });
    };

    // 获取任务类型名称
    const getTaskTypeName = (type) => {
      const typeMap = {
        'COURSE': '课程',
        'TRAIN': '培训',
        'EXAM': '考试',
        'ASSIGNMENT': '作业',
        'SURVEY': '调研'
      };
      return typeMap[type] || type;
    };

    // 获取任务类型颜色
    const getTaskTypeColor = (type) => {
      const colorMap = {
        'COURSE': 'blue',
        'TRAIN': 'purple',
        'EXAM': 'red',
        'ASSIGNMENT': 'green',
        'SURVEY': 'orange'
      };
      return colorMap[type] || 'default';
    };

    // 禁用日期
    const disabledDate = (current) => {
      return current && current < dayjs().startOf('day');
    };

    onMounted(() => {
      // 初始化完成
    });

    return {
      formRef,
      collapsed,
      selectedKeys,
      openKeys,

      dateRange,
      formData,
      rules,
      activeStageKeys,
      taskColumns,
      taskModalVisible,
      currentStageIndex,
      categoryModalVisible,
      submitting,
      selectedTargets,
      selectedEditors,
      selectedUsers,
      hasPermission,
      handleDateRangeChange,
      handleStageTimeChange,
      addStage,
      moveStage,
      removeStage,
      showAddTaskModal,
      handleAddTaskConfirm,
      handleAddTaskCancel,
      moveTask,
      removeTask,
      getTaskTypeName,
      getTaskTypeColor,
      showOrgUserModal,
      handleOrgUserConfirm,
      removeEditor,
      getTargetTypeName,
      handleSubmit,
      handleCancel,
      handleLogout,
      navigateTo,
      showCategoryModal,
      editorSelectionModalVisible,
      showEditorSelectionModal,
      handleEditorSelectionConfirm,
      handleEditorSelectionCancel,
      handleCropSuccess,
      COLLABORATOR_TYPE,
      VISIBILITY_TYPE,
      dayjs,
      disabledDate
    };
  }
});
</script>

<style scoped>
.learning-map-edit-layout {
  min-height: 100vh;
}

.content {
  position: relative;
  padding: 24px 24px 80px;
  background-color: #f0f2f5;
  min-height: 280px;
  overflow: auto;
}

.content-header {
  padding-bottom: 12px;
}

.form-container {
  height: calc(100% - 36px);
  overflow-y: auto;
  background-color: #FFFFFF;
  padding: 24px;
  border-radius: 4px;
}

.form-container .form {
  height: 100%;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.sticky-title {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 100;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.upload-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
}

.task-management {
  margin-top: 16px;
  border-top: 1px dashed #e8e8e8;
  padding-top: 16px;
}

.empty-stages,
.empty-tasks {
  padding: 24px;
  text-align: center;
}

.stages-container {
  margin-bottom: 24px;
}

.tasks-section {
  margin-top: 24px;
}

.selected-targets {
  margin-top: 16px;
}

.footer-actions {
  text-align: left;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: calc(100% - 48px);
  padding: 24px;
}
</style>
