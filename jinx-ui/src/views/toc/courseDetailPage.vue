<template>
  <div class="course-detail-page">
    <!-- 头部导航 -->
    <HeaderComponent activeKey="LEARNING_CENTER" />

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 面包屑 -->
      <Breadcrumb :items="breadcrumbItems" />
      <a-spin :spinning="loading">
        <!-- 课程详情 -->
        <div class="course-detail-container">
          <!-- 课程头部信息 -->
          <div class="course-header">
            <div class="course-title">
              <h1>{{ isSeries ? currentCourse.name : courseDetail.title }}</h1>
            </div>
            <div class="course-meta">
              <span class="creator-info">
                <span v-if="currentCourse?.instructor?.name">
                  {{ currentCourse?.instructor?.name }} {{ formatDate(currentCourse.createdAt) }}
                </span>
              </span>
              <span class="progress-info" v-if="isSeries">
                已学 {{ currentCourse?.progress || 0 }}%
              </span>
              <span class="progress-info" v-else>
                已学 {{ courseDetail.userProgress?.progress || 0 }}%
              </span>
            </div>
          </div>

          <!-- 课程内容区 -->
          <div class="course-content" :class="{ 'with-sidebar': isSeries }">
            <!-- 系列课程侧边栏 -->
            <div v-if="isSeries" class="course-sidebar">
              <div class="sidebar-header">
                <h3>课程目录</h3>
              </div>
              <div class="sidebar-content">
                <div v-for="(item, index) in courseDetail.courseItems" :key="item.id"
                  :class="['sidebar-item', { active: currentCourse.id === item.id }]" @click="switchCourse(item)">
                  <div class="item-index">{{ index + 1 }}</div>
                  <div class="item-info">
                    <div class="item-title">{{ item.name }}</div>
                    <div class="item-type">{{ getTypeText(item.bizType) }}</div>
                  </div>
                  <div class="item-progress" v-if="item.status === 'completed'">
                    <CheckCircleOutlined style="font-size: 20px; color: #1890ff" />
                  </div>
                  <div class="item-progress" v-else>
                    <div class="progress-circle">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 内容展示区 -->
            <div class="content-display">
              <div v-if="currentCourse.bizType === 'article'">
                <iframe :src="currentCourse.url" width="100%" height="600" frameborder="0"
                  crossorigin="anonymous"></iframe>
              </div>
              <!-- 视频播放器 -->
              <div v-if="currentCourse.bizType === 'video'" class="video-container">
                <video ref="videoPlayer" class="video-player" controls :src="currentCourse.url"
                  @timeupdate="handleVideoProgress" @pause="isPaused = true" @play="isPaused = false">
                  <source :src="currentCourse.url" type="video/mp4">
                  您的浏览器不支持视频播放
                </video>
                <div class="play-overlay" v-if="isPaused" @click="playVideo">
                  <div class="play-icon">
                    <PlayCircleFilled />
                  </div>
                </div>
                <div class="video-controls">
                  <div class="speed-control">
                    <div class="current-speed">
                      {{ currentPlaybackRate }}x
                    </div>
                    <div class="speed-options">
                      <div v-for="rate in [1, 1.25, 1.5, 2]" :key="rate"
                        :class="['speed-option', { active: currentPlaybackRate === rate }]"
                        @click="setPlaybackRate(rate)">
                        {{ rate }}x
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- PDF文档查看器 -->
              <div v-else-if="currentCourse.bizType === 'document'" class="document-container">
                <!-- 图片文件直接显示 -->
                <div v-if="isImageFile(currentCourse.url)" class="image-viewer-container">
                  <img :src="currentCourse.url" class="image-viewer" alt="课程图片" />
                </div>
                <!-- PDF文件使用PDF.js -->
                <div v-else-if="isPdfFile(currentCourse.url)" class="pdf-viewer-container">
                  <PDF :src="currentCourse.url" />
                </div>
                <!-- Office文件使用在线预览 -->
                <iframe v-else-if="isOfficeFile(currentCourse.url)" :src="getOfficeViewerUrl(currentCourse.url)"
                  class="pdf-viewer" frameborder="0">
                </iframe>
                <!-- 其他文档类型直接使用iframe -->
                <iframe v-else :src="currentCourse.url" class="pdf-viewer" frameborder="0">
                </iframe>
              </div>
            </div>
          </div>
          <!-- 学习人员 -->
          <!-- <LearnersSection :bizType="'COURSE'" :bizId="courseDetail.id" /> -->
        </div>
      </a-spin>
    </div>

    <!-- 底部 -->
    <common-footer />
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import HeaderComponent from '@/components/common/HeaderComponent.vue';
import CommonFooter from '@/components/common/FooterComponent.vue';
import Breadcrumb from '@/components/common/Breadcrumb.vue';
import LearnersSection from '@/components/common/LearnersSection.vue';
import { getCourseDetail } from '@/api/toc/course';
import { recordLearningProgress } from '@/api/toc/learning';
import { throttle } from 'throttle-debounce';
import { CheckCircleOutlined, SmileOutlined, PauseOutlined, PlayCircleFilled } from '@ant-design/icons-vue';
import { onUnmounted } from 'vue';
import PDF from "pdf-vue3";

// 点击外部指令
const clickOutside = {
  beforeMount(el, binding) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent);
  },
};

export default defineComponent({
  name: 'CourseDetailPage',
  components: {
    HeaderComponent,
    CommonFooter,
    Breadcrumb,
    LearnersSection,
    CheckCircleOutlined,
    SmileOutlined,
    PauseOutlined,
    PlayCircleFilled,
    PDF
  },
  directives: {
    'click-outside': clickOutside
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const videoPlayer = ref(null);
    const currentPlaybackRate = ref(1);
    const currentCourseIndex = ref(0);
    // 记录进度interval对象
    const intervalRecordPageProgressObj = ref(null);

    // 状态
    const loading = ref(false);

    /** 课程详情 */
    const courseDetail = ref({});
    /** 当前课程 */
    const currentCourse = ref({});
    /** 当前课程最大播放时间 */
    const maxWatchTime = ref(0);
    /** 视频是否暂停 */
    const isPaused = ref(false);
    /** 是否是系列课 */
    const isSeries = computed(() => courseDetail.value?.type === 'series');

    // 面包屑数据
    const breadcrumbItems = computed(() => {
      const items = [
        { name: '学习中心', path: '/toc/learning/center' }
      ];

      // 从地图阶段来的情况
      if (route.query.mapId && route.query.mapName && route.query.stageId && route.query.stageName) {
        items.push({
          name: route.query.mapName,
          path: `/toc/map/detail/${route.query.mapId}`
        });
        items.push({
          name: route.query.stageName,
          path: `/toc/map/stage/detail?mapId=${route.query.mapId}&stageId=${route.query.stageId}&mapName=${encodeURIComponent(route.query.mapName)}&stageName=${encodeURIComponent(route.query.stageName)}`
        });
      }
      // 从培训来的情况
      if (route.query.trainId && route.query.trainName) {
        const mapQuery = route.query.mapId ? `?mapId=${route.query.mapId}&mapName=${encodeURIComponent(route.query.mapName)}&stageId=${route.query.stageId}&stageName=${encodeURIComponent(route.query.stageName)}` : '';
        items.push({
          name: route.query.trainName,
          path: `/toc/train/detail/${route.query.trainId}${mapQuery}`
        });
      }

      items.push({
        name: courseDetail.value.title || '课程'
      });

      return items;
    });

    // 方法
    const fetchCourseDetail = async () => {
      const courseId = route.params.id;
      if (!courseId) {
        message.error('课程ID不能为空');
        return;
      }

      loading.value = true;
      try {
        const response = await getCourseDetail(courseId);
        if (response.code === 200 && response.data) {
          courseDetail.value = response.data;
          if (isSeries.value) {
            /** 系列课立即记录一次进度 */
            updatePageProgress(0);
            /** 进来就切换到第一课 */
            switchCourse(response.data?.courseItems[0]);
          } else {
            /** 普通课程从外层取数据 */
            currentCourse.value = response.data?.courseItems[0];
            maxWatchTime.value = response.data?.userProgress?.studyDuration || 0;
            updatePageProgress(100);
          }
        } else {
          message.error(response.message || '获取课程详情失败');
        }
      } catch (error) {
        console.error('获取课程详情异常:', error);
      } finally {
        loading.value = false;
      }
    };

    const formatDate = (dateStr) => {
      if (!dateStr) return '';

      try {
        const date = new Date(dateStr);
        return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (error) {
        return dateStr;
      }
    };

    const getTypeText = (type) => {
      const typeMap = {
        'video': '视频课程',
        'document': '文档',
        'series': '系列课程',
        'article': '文章'
      };
      return typeMap[type] || type;
    };

    const setPlaybackRate = (rate) => {
      if (videoPlayer.value) {
        videoPlayer.value.playbackRate = rate;
        currentPlaybackRate.value = rate;
      }
    };

    const playVideo = () => {
      if (videoPlayer.value) {
        videoPlayer.value.play();
        isPaused.value = false;
      }
    };

    const switchCourse = async (item) => {
      if (currentCourse.value.id === item.id) { return }
      // 切换到新课程
      currentCourse.value = item;
      maxWatchTime.value = item?.duration || 0;
      // 保存当前课程的学习进度
      if (videoPlayer.value && currentCourse.value.bizType === 'video') {
        const progress = (videoPlayer.value.currentTime / videoPlayer.value.duration) * 100;
        await updateCourseProgress(progress);
      }

      /** 如果是文档，则直接更新进度 */
      if (item.bizType === 'document') {
        updateCourseProgress(100);
      }

      // 重置视频播放器
      if (videoPlayer.value) {
        videoPlayer.value.currentTime = 0;
      }

      // 设置视频为暂停状态
      if (item.bizType === 'video') {
        isPaused.value = true;
      } else {
        isPaused.value = false;
      }
    };

    /**
     * 更新课程学习进度
     */
    const updatePageProgress = async (progress, duration) => {
      try {
        const pageId = courseDetail.value.id;
        const data = {
          "contentType": 'COURSE',
          "parentType": '',
          "contentId": pageId,
          "parentId": '',
          "progress": Math.floor(progress),
          "duration": duration || 0
        }
        const response = await recordLearningProgress(data);
        // 文档课程直接更新进度
        if (response.code === 200) {
          console.log('response', response, isSeries.value, courseDetail.value.type);
          if (!isSeries.value && courseDetail.value.type !== 'video') {
            courseDetail.value.userProgress.progress = response.data.progress;
            courseDetail.value.userProgress.status = response.data.isCompleted ? 'completed' : 'learning';
          }
        }
      } catch (error) {
        console.error('记录学习进度失败:', error);
      }
    };

    /**
     * 更新视频课程学习进度
     */
    const updateCourseProgress = throttle(3 * 1000, async (progress) => {
      try {
        const courseId = currentCourse.value.id;
        const parentId = courseDetail.value.id;
        const duration = Math.floor(videoPlayer.value?.currentTime || 0);
        const recordResponse = await recordLearningProgress(
          {
            "contentType": isSeries.value ? 'APPENDIX_FILE' : 'COURSE',  // 附件类型
            "parentType": isSeries.value ? 'SERIES' : '',  // COURSE-课程/SERIES-系列课程/LEARNING_MAP-学习地图/MAP_STAGE-地图阶段/TRAIN-培训
            "contentId": isSeries.value ? courseId : parentId, // 当前节点id, 例如培训场景，这里传课程id
            "parentId": parentId, // 父节点id，例如培训场景，这里传培训id
            "progress": Math.floor(progress), // 进度
            "duration": duration // 学习时长 秒数
          }
        );
        const targetCourse = courseDetail.value.courseItems.find(item => item.id === courseId);
        // 更新最大时长
        targetCourse.duration = maxWatchTime.value > duration ? maxWatchTime.value : duration;
        const currentProgress = Math.floor(recordResponse?.data?.progress);
        // 更新当前课程项的进度
        if (isSeries.value) {
          if (recordResponse?.data?.isCompleted) {
            targetCourse.status = 'completed';
          }

          targetCourse.progress = currentProgress;
        } else {
          courseDetail.value.userProgress.progress = currentProgress;
          if (recordResponse?.data?.isCompleted) {
            courseDetail.value.userProgress.status = 'completed';
          }
        }
      } catch (error) {
        console.error('记录学习进度失败:', error);
      }
    }, { leading: true, trailing: true });

    /**
     * 记录学习进度
     */
    const handleVideoProgress = () => {
      /** 记录当前播放最大时间，如果往后拖拽且大于最大进度，则不更新 */
      if (!videoPlayer.value) return;
      if (videoPlayer.value.currentTime > maxWatchTime.value + 1) {
        videoPlayer.value.currentTime = maxWatchTime.value;
        return
      }
      /** 记录当前播放最大时间 */
      if (videoPlayer.value.currentTime > maxWatchTime.value) {
        maxWatchTime.value = videoPlayer.value.currentTime;
      }

      const progress = (videoPlayer.value.currentTime / videoPlayer.value.duration) * 100;
      const currentProgress = isSeries.value
        ? currentCourse.value?.progress || 0
        : courseDetail.value?.userProgress.progress || 0;

      if (progress > currentProgress) {
        updateCourseProgress(progress);
      }
    };

    // 15秒记录一次学习进度
    const intervalRecordPageProgress = () => {
      intervalRecordPageProgressObj.value = setInterval(() => {
        if (currentCourse.value.bizType !== 'video') {
          updatePageProgress(100, 15);
        }
      }, 15 * 1000);
    };

    // 监听当前课程变化，重置播放速度
    watch(currentCourseIndex, () => {
      currentPlaybackRate.value = 1;
      if (videoPlayer.value) {
        videoPlayer.value.playbackRate = 1;
      }
    });

    // 生命周期钩子
    onMounted(() => {
      fetchCourseDetail();
      intervalRecordPageProgress();
      isPaused.value = true;
    });

    onUnmounted(() => {
      clearInterval(intervalRecordPageProgressObj.value);
    });

    // 判断是否为PDF文件
    const isPdfFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return extension === 'pdf';
    };

    // 判断是否为Office文件
    const isOfficeFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension);
    };

    // 判断是否为图片文件
    const isImageFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
    };

    // 获取Office在线预览URL
    const getOfficeViewerUrl = (url) => {
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
    };

    return {
      loading,
      courseDetail,
      currentCourse,
      currentCourseIndex,
      videoPlayer,
      currentPlaybackRate,
      isSeries,
      breadcrumbItems,
      formatDate,
      getTypeText,
      setPlaybackRate,
      handleVideoProgress,
      switchCourse,
      isPaused,
      playVideo,
      isPdfFile,
      isOfficeFile,
      isImageFile,
      getOfficeViewerUrl,
    };
  }
});
</script>

<style scoped lang="less">
.course-detail-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
}

.main-content {
  flex: 1;
  max-width: 1440px;
  padding: 0 24px 32px;
  margin: 0 auto;
  width: 100%;
}

.course-detail-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.course-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.course-title h1 {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #6b7280;
  font-size: 14px;
}

.creator-info {
  color: #4b5563;
}

.progress-info {
  color: #2563eb;
  font-weight: 500;
}

.course-content {
  display: flex;
  justify-content: center;

  &.with-sidebar {
    .content-display {
      flex: 1;
    }
  }
}

.course-sidebar {
  width: 280px;
  border-right: 1px solid #e5e7eb;
  background-color: #FFF;

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #111827;
    }
  }

  .sidebar-content {
    overflow-y: auto;
    max-height: 600px;
  }

  .sidebar-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f3f4f6;
    }

    &.active {
      background-color: #eff6ff;
      border-left: 3px solid #2563eb;
    }

    .item-index {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #e5e7eb;
      color: #4b5563;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .item-info {
      flex: 1;
      overflow: hidden;

      .item-title {
        font-size: 14px;
        font-weight: 500;
        color: #111827;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item-type {
        font-size: 12px;
        color: #6b7280;
      }
    }

    .item-progress {
      margin-left: 12px;

      .progress-circle {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #e5e7eb;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 2px;
          left: 2px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background-color: #fff;
        }
      }
    }
  }
}

.content-display {
  width: 100%;
  min-height: 600px;
}

.video-container {
  position: relative;
  width: 100%;
  background-color: #000;

  &:hover {
    .video-controls {
      display: block;
    }
  }
}

.video-player {
  width: 100%;
  height: 600px;
  background-color: #000;
}

.video-controls {
  display: none;
  position: absolute;
  bottom: 36px;
  right: 136px;
  z-index: 10;
  width: 60px;
  text-align: center;
}

.speed-control {
  position: relative;
  user-select: none;

  &:hover {
    .speed-options {
      display: block;
    }
  }
}

.current-speed {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.speed-options {
  position: absolute;
  bottom: 100%;
  right: 0;
  // margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  overflow: hidden;
  width: 60px;
  display: none;
}

.speed-option {
  color: white;
  padding: 8px 12px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &.active {
    background-color: rgba(24, 144, 255, 0.6);
    font-weight: 500;
  }
}

.document-container {
  width: 100%;
  height: 800px;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  border: none;
}

.pdf-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.image-viewer {
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-canvas {
  flex: 1;
  width: 100%;
  max-width: 100%;
  height: auto;
  background-color: #fff;
  border: 1px solid #e5e7eb;
}

.pdf-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
  min-width: 100px;
  text-align: center;
  font-weight: 500;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.3s ease;
  cursor: pointer;

  .play-icon {
    font-size: 80px;
    color: #ffffff;
    opacity: 0.9;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;

    &:hover {
      transform: scale(1.1);
      opacity: 1;
      color: #1890ff;
      animation: none;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}
</style>
