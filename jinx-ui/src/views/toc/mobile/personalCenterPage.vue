<template>
  <div class="personal-center-mobile-page">
    <!-- 顶部个人信息区域 -->
    <div class="user-profile-section">
      <div class="user-profile-container">
        <div class="user-avatar">
          <a-avatar :size="64" :src="userProfile.userInfo?.avatar">
            <template #icon><user-outlined /></template>
          </a-avatar>
        </div>
        <div class="user-info">
          <div class="user-name-level">
            <h2 class="user-name">{{ userProfile.userInfo?.nickname || '用户' }}</h2>
          </div>
          <div class="credit-info">
            <span class="credit-value">{{ userProfile.statistics?.totalCredit || 0 }}</span>
            <span class="credit-label">学分</span>
          </div>
        </div>
      </div>

      <!-- 快捷入口区域 -->
      <div class="quick-links">
        <div class="quick-link-item" @click="goToLearningTasks">
          <div class="quick-link-icon">
            <schedule-outlined />
          </div>
          <div class="quick-link-text">我的学习任务</div>
          <right-outlined class="quick-link-arrow" />
        </div>
      </div>

      <!-- 学习统计数据区域 -->
      <div class="statistics-cards">
        <div class="stat-card">
          <div class="stat-icon">
            <clock-circle-outlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userProfile.statistics?.totalStudyTime || 0 }}</div>
            <div class="stat-label">学习时长<br/>(分钟)</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <book-outlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userProfile.statistics?.completedTrainCount || 0 }}</div>
            <div class="stat-label">完成培训</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <deployment-unit-outlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userProfile.statistics?.completedMapCount || 0 }}</div>
            <div class="stat-label">完成地图</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <trophy-outlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userProfile.statistics?.certificateCount || 0 }}</div>
            <div class="stat-label">获得证书</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习记录区域 -->
    <div class="learning-records-section">
      <!-- 标题 -->
      <div class="section-title">
        <div class="title">学习记录</div>
      </div>

      <!-- 学习记录列表 -->
      <div class="learning-records-list-horizontal" v-if="learningRecords.length > 0">
        <div v-for="record in learningRecords" :key="record.id" class="learning-record-card-horizontal" @click="navigateToDetail(record)">
          <div class="record-image-horizontal">
            <img :src="record.coverUrl || 'https://via.placeholder.com/80x60'" alt="封面图" />
            <a-tag :color="getTypeColor(record.type)" class="record-tag-horizontal">{{ getTypeLabel(record.type) }}</a-tag>
          </div>
          <div class="record-info-horizontal">
            <h3 class="record-title">{{ record.contentName }}</h3>
            <p class="record-desc">{{ record.description }}</p>
            <div class="record-progress">
              <div class="progress-bar">
                <div class="progress-inner" :style="{ width: record.progress + '%' }"></div>
              </div>
              <div class="progress-details">
                <span class="progress-text">{{ getProgressText(record) }}</span>
                <span class="credit-text">{{ record.credit }} 学分</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <empty-state v-else title="暂无学习记录" description="您还没有任何学习记录" />

      <!-- 加载更多提示 -->
      <div class="loading-more" v-if="learningRecords.length > 0 && hasMoreRecords && loading">
        <a-spin size="small" />
        <span>加载中...</span>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <MobileTabBar active="profile" />

    <!-- 底部占位，防止内容被底部导航栏遮挡 -->
    <div class="bottom-placeholder"></div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, reactive, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import MobileTabBar from '@/components/mobile/MobileTabBar.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import { getUserProfile, getUserLearningRecords, getUserLearningTotal } from '@/api/toc/personal';
import {
  UserOutlined,
  ClockCircleOutlined,
  BookOutlined,
  DeploymentUnitOutlined,
  TrophyOutlined,
  ScheduleOutlined,
  RightOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'PersonalCenterMobilePage',
  components: {
    MobileTabBar,
    EmptyState,
    UserOutlined,
    ClockCircleOutlined,
    BookOutlined,
    DeploymentUnitOutlined,
    TrophyOutlined,
    ScheduleOutlined,
    RightOutlined
  },
  setup() {
    const router = useRouter();

    // 用户个人中心数据
    const userProfile = ref({
      userInfo: {},
      statistics: {},
      certificates: [],
      recentLearning: []
    });

    // 学习记录
    const learningRecords = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(5); // 移动端每页显示较少的记录，但增加数量以便触底加载
    const totalRecords = ref(0);
    const loading = ref(false);
    const hasMoreRecords = computed(() => {
      return learningRecords.value.length < totalRecords.value;
    });

    // 用户学习统计数据
    const userLearningTotal = ref({
      allTotal: 0,
      courseTotal: 0,
      trainTotal: 0,
      mapTotal: 0
    });

    // 当前选中的标签和筛选状态（保留但不使用）
    const activeTab = ref('ALL');
    const filterStatus = ref('ALL');

    // 获取等级称号
    const getLevelTitle = (level) => {
      const titles = [''];
      return titles[level] || '';
    };

    // 获取内容类型标签
    const getTypeLabel = (type) => {
      const typeMap = {
        'COURSE': '课程',
        'TRAIN': '培训',
        'LEARNING_MAP': '地图',
        'EXAM': '考试',
        'PRACTICE': '练习'
      };
      return typeMap[type] || '未知类型';
    };

    // 获取任务类型颜色
    const getTypeColor = (type) => {
      const colorMap = {
        'TRAIN': 'blue',
        'LEARNING_MAP': 'purple',
        'EXAM': 'orange',
        'PRACTICE': 'green'
      };
      return colorMap[type] || 'default';
    };


    // 获取进度文本
    const getProgressText = (record) => {
      if (record.status === 1) {
        return '已完成';
      } else if (record.progress > 0) {
        return `进度 ${record.progress}%`;
      } else {
        return '未开始';
      }
    };

    // 获取用户个人中心数据
    const fetchUserProfile = async () => {
      try {
        const response = await getUserProfile();
        if (response.code === 200) {
          userProfile.value = response.data;
        } else {
          message.error(response.message || '获取个人中心数据失败');
        }
      } catch (error) {
        console.error('获取个人中心数据异常:', error);
      }
    };

    // 获取用户学习统计数据
    const fetchUserLearningTotal = async () => {
      try {
        const response = await getUserLearningTotal(true);
        if (response.code === 200) {
          userLearningTotal.value = response.data;
        } else {
          message.error(response.message || '获取学习统计数据失败');
        }
      } catch (error) {
        console.error('获取学习统计数据异常:', error);
      }
    };

    // 获取学习记录
    const fetchLearningRecords = async (isLoadMore = false) => {
      try {
        loading.value = true;
        const params = {
          type: '', // 获取所有类型
          source: undefined, // 不筛选来源
          status: 0, // 全部状态
          pageNum: isLoadMore ? currentPage.value : 1,
          pageSize: pageSize.value
        };

        const response = await getUserLearningRecords(params);
        if (response.code === 200) {
          if (isLoadMore) {
            // 追加数据
            learningRecords.value = [...learningRecords.value, ...response.data.list];
          } else {
            // 替换数据
            learningRecords.value = response.data.list;
            currentPage.value = 1;
          }
          totalRecords.value = response.data.total;
        } else {
          message.error(response.message || '获取学习记录失败');
        }
      } catch (error) {
        console.error('获取学习记录异常:', error);
      } finally {
        loading.value = false;
      }
    };

    // 监听页面滚动，实现触底加载更多
    const handleScroll = () => {
      // 如果正在加载更多，或者没有更多数据了，则不执行
      if (loading.value || !hasMoreRecords.value) return;

      // 获取页面滚动位置
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      // 获取页面可见区域高度
      const windowHeight = window.innerHeight;
      // 获取页面总高度
      const documentHeight = document.documentElement.scrollHeight;

      // 检测是否滚动到底部(距离底部100px时就开始加载)
      if (scrollTop + windowHeight >= documentHeight - 100) {
        loadMore();
      }
    };

    // 处理标签切换（已移除，保留函数以防其他地方调用）
    const handleTabChange = (tab) => {
      // 功能已移除
    };

    // 处理筛选状态变更（已移除，保留函数以防其他地方调用）
    const handleFilterChange = (status) => {
      // 功能已移除
    };

    // 加载更多
    const loadMore = () => {
      if (loading.value || !hasMoreRecords.value) return;
      loading.value = true;
      currentPage.value += 1;
      fetchLearningRecords(true);
    };

    // 跳转到详情页
    const navigateToDetail = (record) => {
      const routes = {
        'COURSE': `/toc/mobile/course/${record.contentId}`,
        'TRAIN': `/toc/mobile/train/${record.contentId}`,
        'LEARNING_MAP': `/toc/mobile/map/${record.contentId}`,
        'EXAM': `/toc/mobile/exam/${record.contentId}`,
        'PRACTICE': `/toc/mobile/practice/${record.contentId}`
      };

      const route = routes[record.type];
      if (route) {
        router.push(route);
      } else {
        message.warning('暂不支持查看该类型的详情');
      }
    };

    onMounted(() => {
      fetchUserProfile();
      fetchLearningRecords();
      fetchUserLearningTotal();

      // 添加滚动事件监听
      window.addEventListener('scroll', handleScroll);
    });

    // 组件卸载时，移除滚动事件监听
    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll);
    });


    // 跳转到学习任务页面
    const goToLearningTasks = () => {
      router.push('/toc/mobile/learning/tasks');
    };

    return {
      userProfile,
      learningRecords,
      loading,
      hasMoreRecords,
      activeTab,
      filterStatus,
      userLearningTotal,
      getLevelTitle,
      getTypeLabel,
      getTypeColor,
      getProgressText,
      handleTabChange,
      handleFilterChange,
      loadMore,
      navigateToDetail,
      goToLearningTasks
    };
  }
});
</script>

<style scoped>
.personal-center-mobile-page {
  min-height: 100vh;
  background-color: #F9FAFB;
  padding-bottom: 65px; /* 为底部导航栏留出空间 */
}

/* 个人信息区域样式 */
.user-profile-section {
  background-color: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.user-profile-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.user-avatar {
  margin-right: 16px;
}

.user-info {
  flex: 1;
}

.user-name-level {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.user-name {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  margin-right: 8px;
}

/* 学分信息样式 */
.credit-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.credit-value {
  font-size: 18px;
  font-weight: 500;
  color: #1890FF;
}

.credit-label {
  font-size: 12px;
  color: #6B7280;
}

/* 快捷入口区域样式 */
.quick-links {
  margin: 16px 0;
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #F3F4F6;
}

.quick-link-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
  cursor: pointer;
}

.quick-link-item:last-child {
  border-bottom: none;
}

.quick-link-icon {
  font-size: 20px;
  color: #1890FF;
  margin-right: 12px;
}

.quick-link-text {
  flex: 1;
  font-size: 15px;
  color: #111827;
}

.quick-link-arrow {
  color: #9CA3AF;
  font-size: 14px;
}

/* 统计卡片样式 */
.statistics-cards {
  display: flex;
  gap: 12px;
}

.stat-card {
  background-color: #FFFFFF;
  border: 1px solid #F3F4F6;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex: 1;
}

.stat-icon {
  font-size: 24px;
  color: #1890FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 2px;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6B7280;
  text-align: center;
}

/* 学习记录区域样式 */
.learning-records-section {
  background-color: #FFFFFF;
  padding: 16px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.learning-records-list-horizontal {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.learning-record-card-horizontal {
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  overflow: hidden;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 12px;
}

.record-image-horizontal {
  width: 80px;
  height: 60px;
  position: relative;
  background-color: #F3F4F6;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 12px;
}

.record-image-horizontal img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.record-tag-horizontal {
  position: absolute;
  top: 4px;
  left: 4px;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  transform: scale(0.8);
  transform-origin: left top;
}

.record-info-horizontal {
  flex: 1;
  min-width: 0;
}

.record-info-horizontal .record-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #000000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.record-info-horizontal .record-desc {
  font-size: 11px;
  color: #6B7280;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  max-height: 28px;
}

.record-info-horizontal .record-progress {
  margin-top: 8px;
}

.record-info-horizontal .progress-bar {
  height: 3px;
  background-color: #E5E7EB;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-inner {
  height: 100%;
  background-color: #1890FF;
  border-radius: 2px;
}

.record-info-horizontal .progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-info-horizontal .progress-text {
  font-size: 11px;
  color: #6B7280;
}

.record-info-horizontal .credit-text {
  font-size: 11px;
  color: #1890FF;
}

/* 加载更多提示 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  gap: 8px;
  color: #9CA3AF;
  font-size: 14px;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.bottom-placeholder {
  height: 65px; /* 与底部导航栏高度一致 */
}
</style>
