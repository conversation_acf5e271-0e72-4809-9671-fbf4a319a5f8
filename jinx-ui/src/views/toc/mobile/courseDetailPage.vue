<template>
  <div class="course-detail-mobile-page">
    <!-- 主体内容 -->
    <div class="main-content">
      <a-spin :spinning="loading">
        <!-- 课程详情 -->
        <div class="course-detail-container">
          <!-- 课程头部信息 -->
          <div class="course-header">
            <div class="course-meta">
              <div class="creator-info">
                <span v-if="currentCourse?.instructor?.name">
                  <user-outlined />
                  {{ currentCourse?.instructor?.name }}
                </span>
              </div>
              <div class="date-info">
                <calendar-outlined />
                {{ formatDate(currentCourse.createdAt) }}
              </div>
              <div class="progress-info" v-if="isSeries">
                <progress-outlined />
                已学 {{ currentCourse?.progress || 0 }}%
              </div>
              <div class="progress-info" v-else>
                <progress-outlined />
                已学 {{ courseDetail.userProgress?.progress || 0 }}%
              </div>
            </div>
          </div>

          <!-- 内容展示区 -->
          <div class="content-display">
            <div v-if="currentCourse.bizType === 'article'">
              <iframe :src="currentCourse.url" width="100%"
                height="600" frameborder="0" crossorigin="anonymous"></iframe>
            </div>
            <!-- 视频播放器 -->
            <div v-if="currentCourse.bizType === 'video'" class="video-container">
              <video ref="videoPlayer" class="video-player" controls :src="currentCourse.url"
                @timeupdate="handleVideoProgress" @pause="isPaused = true" @play="isPaused = false" playsinline webkit-playsinline>
                <source :src="currentCourse.url" type="video/mp4">
                您的浏览器不支持视频播放
              </video>
              <div class="play-overlay" v-if="isPaused" @click="playVideo">
                <div class="play-icon">
                  <PlayCircleFilled />
                </div>
              </div>
              <div class="video-controls">
                <div class="speed-control">
                  <div class="current-speed">
                    倍速播放
                  </div>
                  <div class="speed-options">
                    <div v-for="rate in [1, 1.25, 1.5, 2]" :key="rate"
                      :class="['speed-option', { active: currentPlaybackRate === rate }]"
                      @click.stop="setPlaybackRate(rate)">
                      {{ rate }}x
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- PDF文档查看器 -->
            <div v-else-if="currentCourse.bizType === 'document'" class="document-container">
              <!-- 图片文件直接显示 -->
              <div v-if="isImageFile(currentCourse.url)" class="image-viewer-container">
                <img :src="currentCourse.url" class="image-viewer" alt="课程图片" />
              </div>
              <!-- PDF文件使用PDF.js -->
              <div v-else-if="isPdfFile(currentCourse.url)" class="pdf-viewer-container">
                <PDF :src="currentCourse.url" />
              </div>
              <!-- Office文件使用在线预览 -->
              <iframe v-else-if="isOfficeFile(currentCourse.url)"
                :src="getOfficeViewerUrl(currentCourse.url)"
                class="pdf-viewer"
                frameborder="0">
              </iframe>
              <!-- 其他文档类型直接使用iframe -->
              <iframe v-else
                :src="currentCourse.url"
                class="pdf-viewer"
                frameborder="0">
              </iframe>
            </div>
          </div>

          <!-- 系列课程目录 -->
          <div v-if="courseDetail.type === 'series'" class="course-catalog">
            <div class="catalog-header">
              <h3>课程目录</h3>
            </div>
            <div class="catalog-content">
              <div v-for="(item, index) in courseDetail.courseItems" :key="item.id"
                :class="['catalog-item', { active: currentCourse.id === item.id }]" @click="switchCourse(item)">
                <div class="item-index">{{ index + 1 }}</div>
                <div class="item-info">
                  <div class="item-title">{{ item.name }}</div>
                  <div class="item-type">{{ getTypeText(item.bizType) }}</div>
                </div>
                <div class="item-status">
                  <check-circle-filled v-if="item.status === 'completed'" class="completed-icon" />
                  <div v-else class="progress-circle">{{ item.progress || 0 }}%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 学习人员 -->
          <!-- <LearnersSection :bizType="'COURSE'" :bizId="courseDetail.id" /> -->
        </div>
      </a-spin>
    </div>

    <!-- 底部导航栏 -->
    <MobileTabBar active="learning" />

    <!-- 悬浮目录按钮 -->
    <div v-if="courseDetail.type === 'series'" class="floating-catalog-button" @click="toggleCatalogModal">
      <unordered-list-outlined />
    </div>

    <!-- 简略目录弹窗 -->
    <a-modal v-model:visible="showCatalogModal" :footer="null" :closable="true" :mask-closable="true"
      class="catalog-modal" title="课程目录">
      <div class="mini-catalog-list">
        <div v-for="(item, index) in courseDetail.courseItems" :key="item.id"
          :class="['mini-catalog-item', { active: currentCourse.id === item.id }]" @click="switchCourseAndClose(item)">
          <div class="mini-item-index">{{ index + 1 }}</div>
          <div class="mini-item-title">{{ item.name }}</div>
          <div class="mini-item-type">{{ getTypeText(item.bizType) }}</div>
          <div class="mini-item-status">
            <check-circle-filled v-if="item.status === 'completed'" class="mini-completed-icon" />
            <div v-else class="mini-progress">{{ item.progress || 0 }}%</div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, watch, onUnmounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import MobileTabBar from '@/components/mobile/MobileTabBar.vue';
import LearnersSection from '@/components/common/LearnersSection.vue';
import { getCourseDetail } from '@/api/toc/course';
import { recordLearningProgress } from '@/api/toc/learning';
import { throttle } from 'throttle-debounce';
import {
  UserOutlined,
  CalendarOutlined,
  CheckCircleFilled,
  ProgressOutlined,
  UnorderedListOutlined,
  SmileOutlined,
  PlayCircleFilled
} from '@ant-design/icons-vue';
import PDF from "pdf-vue3";

export default defineComponent({
  name: 'CourseDetailMobilePage',
  components: {
    MobileTabBar,
    LearnersSection,
    UserOutlined,
    CalendarOutlined,
    CheckCircleFilled,
    ProgressOutlined,
    UnorderedListOutlined,
    SmileOutlined,
    PlayCircleFilled,
    PDF
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const videoPlayer = ref(null);
    const currentPlaybackRate = ref(1);
    const showSpeedOptions = ref(false);
    const showCatalogModal = ref(false);
    /** 记录进度interval对象 */
    const intervalRecordPageProgressObj = ref(null);

    // 状态
    const loading = ref(false);

    /** 课程详情 */
    const courseDetail = ref({});
    /** 当前课程 */
    const currentCourse = ref({});
    /** 当前课程最大播放时间 */
    const maxWatchTime = ref(0);
    /** 是否暂停 */
    const isPaused = ref(false);
    /** 是否是系列课 */
    const isSeries = computed(() => courseDetail.value?.type === 'series');

    // 方法
    const fetchCourseDetail = async () => {
      const courseId = route.params.id;
      if (!courseId) {
        message.error('课程ID不能为空');
        return;
      }

      loading.value = true;
      try {
        const response = await getCourseDetail(courseId);
        if (response.code === 200 && response.data) {
          courseDetail.value = response.data;
          if (isSeries.value) {
            /** 系列课立即记录一次进度 */
            updatePageProgress(0);
            /** 进来就切换到第一课 */
            switchCourse(response.data?.courseItems[0]);
          } else {
            /** 普通课程从外层取数据 */
            currentCourse.value = response.data?.courseItems[0];
            maxWatchTime.value = response.data?.userProgress?.studyDuration || 0;
            updatePageProgress(100);
          }
        } else {
          message.error(response.message || '获取课程详情失败');
        }
      } catch (error) {
        console.error('获取课程详情异常:', error);
      } finally {
        loading.value = false;
      }
    };

    const formatDate = (dateStr) => {
      if (!dateStr) return '';

      try {
        const date = new Date(dateStr);
        return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (error) {
        return dateStr;
      }
    };

    const getTypeText = (type) => {
      const typeMap = {
        'video': '视频课程',
        'document': '文档',
        'series': '系列课程',
        'article': '文章'
      };
      return typeMap[type] || type;
    };

    // 切换课程
    const switchCourse = (course) => {
      if (currentCourse.value.id === course.id) return;

      // 切换课程
      currentCourse.value = course;
      maxWatchTime.value = course?.duration || 0;
      // 保存当前课程的学习进度
      if (videoPlayer.value && currentCourse.value.bizType === 'video') {
        const progress = (videoPlayer.value.currentTime / videoPlayer.value.duration) * 100;
        updateCourseProgress(progress);
      }

      /** 如果是文档，则直接更新进度 */
      if (course.bizType === 'document') {
        updateCourseProgress(100);
      }

      // 重置视频播放器状态
      if (videoPlayer.value) {
        videoPlayer.value.currentTime = 0;
        maxWatchTime.value = 0;
      }

      // 设置视频为暂停状态
      if (course.bizType === 'video') {
        isPaused.value = true;
      } else {
        isPaused.value = false;
      }
    };

    // 处理视频进度
    const handleVideoProgress = () => {
      /** 记录当前播放最大时间，如果往后拖拽且大于最大进度，则不更新 */
      if (!videoPlayer.value) return;
      if (videoPlayer.value.currentTime > maxWatchTime.value + 1) {
        videoPlayer.value.currentTime = maxWatchTime.value;
        return
      }
      /** 记录当前播放最大时间 */
      if (videoPlayer.value.currentTime > maxWatchTime.value) {
        maxWatchTime.value = videoPlayer.value.currentTime;
      }

      const progress = (videoPlayer.value.currentTime / videoPlayer.value.duration) * 100;
      const currentProgress = courseDetail.value.type === 'series'
        ? currentCourse.value?.progress || 0
        : courseDetail.value?.userProgress.progress || 0;

      if (progress > currentProgress) {
        updateCourseProgress(progress);
      }
    };

    /**
     * 更新课程学习进度
     */
    const updatePageProgress = async (progress, duration) => {
      try {
        const pageId = courseDetail.value.id;
        const data = {
          "contentType": 'COURSE',
          "parentType": '',
          "contentId": pageId,
          "parentId": '',
          "progress": Math.floor(progress),
          "duration": duration || 0
        }
        const response = await recordLearningProgress(data);
        // 文档课程直接更新进度
        if (response.code === 200) {
          console.log('response', response, isSeries.value, courseDetail.value.type);
          if (!isSeries.value && courseDetail.value.type !== 'video') {
            courseDetail.value.userProgress.progress = response.data.progress;
            courseDetail.value.userProgress.status = response.data.isCompleted ? 'completed' : 'learning';
          }
        }
      } catch (error) {
        console.error('记录学习进度失败:', error);
      }
    };

    /**
     * 更新视频课程学习进度
     */
    const updateCourseProgress = throttle(3 * 1000, async (progress) => {
      try {
        const courseId = currentCourse.value.id;
        const parentId = courseDetail.value.id;
        const recordResponse = await recordLearningProgress(
          {
            "contentType": isSeries.value ? 'APPENDIX_FILE' : 'COURSE',  // 附件类型
            "parentType": isSeries.value ? 'SERIES' : '',  // COURSE-课程/SERIES-系列课程/LEARNING_MAP-学习地图/MAP_STAGE-地图阶段/TRAIN-培训
            "contentId": isSeries.value ? courseId : parentId, // 当前节点id, 例如培训场景，这里传课程id
            "parentId": parentId, // 父节点id，例如培训场景，这里传培训id
            "progress": Math.floor(progress), // 进度
            "duration": Math.floor(videoPlayer.value ? videoPlayer.value.currentTime : 0) // 学习时长 秒数
          }
        );

        const currentProgress = Math.floor(recordResponse?.data?.progress);
        // 更新当前课程项的进度
        if (isSeries.value) {
          const targetCourse = courseDetail.value.courseItems.find(item => item.id === courseId);
          if (recordResponse?.data?.isCompleted) {
            targetCourse.status = 'completed';
          }

          targetCourse.progress = currentProgress;
        } else {
          courseDetail.value.userProgress.progress = currentProgress;
          if (recordResponse?.data?.isCompleted) {
            courseDetail.value.userProgress.status = 'completed';
          }
        }
      } catch (error) {
        console.error('记录学习进度失败:', error);
      }
    }, { leading: true, trailing: true });

    // 设置播放速率
    const setPlaybackRate = (rate) => {
      if (!videoPlayer.value) return;

      currentPlaybackRate.value = rate;
      videoPlayer.value.playbackRate = rate;
      showSpeedOptions.value = false;
    };

    // 切换速度选项显示
    const toggleSpeedOptions = () => {
      showSpeedOptions.value = !showSpeedOptions.value;
    };

    // 点击外部关闭速度选项
    const handleClickOutside = (event) => {
      const speedControl = document.querySelector('.speed-control');
      if (speedControl && !speedControl.contains(event.target)) {
        showSpeedOptions.value = false;
      }
    };

    // 切换目录弹窗显示状态
    const toggleCatalogModal = () => {
      showCatalogModal.value = !showCatalogModal.value;
    };

    // 切换课程并关闭弹窗
    const switchCourseAndClose = (course) => {
      switchCourse(course);
      showCatalogModal.value = false;
    };

    // 15秒记录一次学习进度
    const intervalRecordPageProgress = () => {
      intervalRecordPageProgressObj.value = setInterval(() => {
        if (currentCourse.value.bizType !== 'video') {
          updatePageProgress(100,  15);
        }
      }, 15 * 1000);
    };

    const playVideo = () => {
      if (videoPlayer.value) {
        videoPlayer.value.play();
        isPaused.value = false;
      }
    };

    // 判断是否为PDF文件
    const isPdfFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return extension === 'pdf';
    };

    // 判断是否为Office文件
    const isOfficeFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension);
    };

    // 判断是否为图片文件
    const isImageFile = (url) => {
      if (!url) return false;
      const extension = url.toLowerCase().split('.').pop();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
    };

    // 获取Office在线预览URL
    const getOfficeViewerUrl = (url) => {
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
    };

    onMounted(() => {
      fetchCourseDetail();
      intervalRecordPageProgress();
      document.addEventListener('click', handleClickOutside);
      isPaused.value = true;
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
      clearInterval(intervalRecordPageProgressObj.value);
    });

    // 监听路由变化，重新获取课程详情
    watch(() => route.params.id, (newId, oldId) => {
      if (newId !== oldId) {
        fetchCourseDetail();
      }
    });

    // 面包屑数据
    const breadcrumbItems = computed(() => {
      const items = [
        { name: '学习中心', path: '/toc/mobile/learning/center' }
      ];

      // 从培训来的情况
      if (route.query.trainId && route.query.trainName) {
        items.push({
          name: route.query.trainName,
          path: `/toc/mobile/train/${route.query.trainId}`
        });
        items.push({
          name: courseDetail.value.title || '课程'
        });
      }
      // 从地图阶段来的情况
      else if (route.query.mapId && route.query.mapName && route.query.stageId && route.query.stageName) {
        items.push({
          name: route.query.mapName,
          path: `/toc/mobile/map/${route.query.mapId}`
        });
        items.push({
          name: route.query.stageName,
          path: `/toc/mobile/map/stage?mapId=${route.query.mapId}&stageId=${route.query.stageId}&mapName=${encodeURIComponent(route.query.mapName)}&stageName=${encodeURIComponent(route.query.stageName)}`
        });
        items.push({
          name: courseDetail.value.title || '课程'
        });
      }
      // 直接访问课程的情况
      else {
        items.push({
          name: courseDetail.value.title || '课程'
        });
      }

      return items;
    });

    return {
      loading,
      courseDetail,
      currentCourse,
      videoPlayer,
      currentPlaybackRate,
      showSpeedOptions,
      showCatalogModal,
      isSeries,
      formatDate,
      getTypeText,
      switchCourse,
      switchCourseAndClose,
      handleVideoProgress,
      setPlaybackRate,
      toggleSpeedOptions,
      toggleCatalogModal,
      isPaused,
      playVideo,
      breadcrumbItems,
      // PDF相关
      isPdfFile,
      isOfficeFile,
      isImageFile,
      getOfficeViewerUrl,
    };
  }
});
</script>

<style scoped>
.course-detail-mobile-page {
  padding-bottom: 65px;
}

.main-content {
  padding: 0;
}

.course-detail-container {
  background-color: #fff;
}

.course-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.creator-info,
.date-info,
.progress-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.content-display {
  width: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 230px;
  display: block;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.play-icon {
  font-size: 60px;
  color: #ffffff;
  opacity: 0.9;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.video-controls {
  bottom: 60px;
  right: 10px;
  z-index: 10;
}

.speed-control {
  position: relative;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 0px 10px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: start;
}

.current-speed {
  font-size: 12px;
  margin-right: 5px;
}

.speed-options {
  display: flex;
  bottom: 100%;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.speed-option {
  padding: 6px 12px;
  text-align: center;
  cursor: pointer;
}

.speed-option.active {
  color: #1890ff;
}

.document-container {
  width: 100%;
  height: 100vh;
  max-height: 500px;
  overflow: hidden;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  border: none;
}

.pdf-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 16px;
}

.image-viewer {
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-canvas {
  flex: 1;
  width: 100%;
  max-width: 100%;
  height: auto;
  background-color: #fff;
}

.pdf-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  background-color: #f5f5f5;
  border-top: 1px solid #e5e5e5;
}

.page-info {
  font-size: 14px;
  color: #666;
  min-width: 80px;
  text-align: center;
}

.course-catalog {
  padding: 16px;
}

.catalog-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.catalog-header h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.catalog-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.catalog-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s;
}

.catalog-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.item-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  overflow: hidden;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-type {
  font-size: 12px;
  color: #666;
}

.item-status {
  margin-left: 8px;
}

.completed-icon {
  color: #52c41a;
  font-size: 20px;
}

.progress-circle {
  font-size: 12px;
  color: #1890ff;
}

/* 悬浮目录按钮样式 */
.floating-catalog-button {
  position: fixed;
  right: 16px;
  bottom: 80px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.floating-catalog-button:active {
  transform: scale(0.95);
  background-color: #096dd9;
}

/* 简略目录弹窗样式 */
:deep(.catalog-modal .ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.catalog-modal .ant-modal-body) {
  padding: 12px;
  max-height: 60vh;
  overflow-y: auto;
}

.mini-catalog-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mini-catalog-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mini-catalog-item:active {
  background-color: #e6f7ff;
}

.mini-catalog-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.mini-item-index {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
  flex-shrink: 0;
}

.mini-item-title {
  flex: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mini-item-type {
  font-size: 12px;
  color: #666;
}

.mini-item-status {
  margin-left: 8px;
  flex-shrink: 0;
}

.mini-completed-icon {
  color: #52c41a;
}

.mini-progress {
  font-size: 12px;
  color: #1890ff;
}


</style>