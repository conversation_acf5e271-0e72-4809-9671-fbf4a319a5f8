import { CalculatorOutlined, FileOutlined, FileTextOutlined, NodeIndexOutlined, PlaySquareOutlined, VideoCameraOutlined } from "@ant-design/icons-vue";

// 根据课程类型获取图标组件
export const getCourseTypeIcon = ({ type, contentType }) => {
  const iconMap = {
    'video': VideoCameraOutlined,
    'article': FileTextOutlined,
    'document': FileOutlined,
    'series': PlaySquareOutlined,
    'TRAIN': CalculatorOutlined,
    'LEARNING_MAP': NodeIndexOutlined,
  };
  if (type === 'COURSE') {
    return iconMap[contentType] || FileTextOutlined;
  }
  return iconMap[type] || FileTextOutlined;
};

// 根据课程类型获取显示文字
export const getCourseTypeText = ({ type, contentType, contentCount }) => {
  const textMap = {
    'LEARNING_MAP': `${contentCount} 个阶段`,
    'TRAIN': `${contentCount} 门课`,
  };

  const courseTextMap = {
    'video': '视频',
    'article': '文章',
    'document': '文档',
    'series': '系列课'
  }
  if (type === 'COURSE') {
    return courseTextMap[contentType];
  }

  return textMap[type] || '';
};