mos.config.enabled=true
mos.config.data-id=jinx.properties
mos.boot.event.enabled=true
mos.kv.enabled=true
#spring.datasource.url=**********************************

mos.boot.event.remotes[0].topic=USER_LEARN_EVENT
mos.boot.event.remotes[0].consumerGroup=GID_USER_LEARN_EVENT


mos.db.enabled=true
mos.db.configs[0].name=JINX_APP
mos.db.configs[0].primary=true
mos.db.configs[0].type=yun
mos.db.configs[0].mapperLocations=classpath*:mapper/*.xml
mos.db.configs[0].mapperPackages=com.learn.infrastructure.repository.mapper
mos.db.configs[0].typeAliasesPackages=com.learn.infrastructure.repository.entity


