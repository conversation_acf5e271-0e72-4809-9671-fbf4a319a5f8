package com.learn.infrastructure.repository.mapper;

import com.learn.infrastructure.repository.entity.AssignmentDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【assignment_detail(指派明细表)】的数据库操作Mapper
* @createDate 2025-04-17 14:36:42
* @Entity com.learn.infrastructure.repository.entity.AssignmentDetail
*/
public interface AssignmentDetailMapper extends BaseMapper<AssignmentDetail> {

    /**
     * 统计指派记录的成功和失败人数
     * 按照record_id+user_id去重复
     * 
     * @param assignRecordId 指派记录ID
     * @return 包含successCount和failedCount的Map
     */
    Map<String, BigDecimal> countSuccessAndFailedByRecordId(@Param("assignRecordId") Long assignRecordId);

    /**
     * 批量插入或更新指派明细
     * 使用on duplicate key update实现
     * 
     * @param detailList 指派明细列表
     * @return 影响的行数
     */
    int batchInsertOrUpdateAssignmentDetail(@Param("detailList") List<AssignmentDetail> detailList);

}




