package com.learn.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learn.infrastructure.repository.entity.UserLearningTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户学习记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Mapper
public interface UserLearningTaskMapper extends BaseMapper<UserLearningTask> {
    
    /**
     * 统计学习人数和完成人数
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return Map包含learnerCount(学习人数)和completionCount(完成人数)
     */
    Map<String, Object> countLearnerAndCompletion(@Param("bizType") String bizType, @Param("bizId") Long bizId);
    
    /**
     * 统计学习时长
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return Map包含totalDuration(总学习时长)和avgDuration(平均学习时长)
     */
    Map<String, Object> countDuration(@Param("bizType") String bizType, @Param("bizId") Long bizId);
    
    /**
     * 按内容类型统计完成率
     *
     * @param parentType 父业务类型
     * @param parentId 父业务ID
     * @return 按内容类型分组的统计结果列表
     */
    List<Map<String, Object>> countCompletionByContentType(@Param("parentType") String parentType, @Param("parentId") Long parentId);
    
    /**
     * 按日期统计学习分布
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 按日期分组的统计结果列表
     */
    List<Map<String, Object>> countTimeDistribution(@Param("bizType") String bizType, @Param("bizId") Long bizId);
    
    /**
     * 按业务类型统计用户学习记录数量
     *
     * @param userId 用户ID
     * @return 包含各类型学习记录数量的Map
     */
    Map<String, Object> countUserLearningByTypes(@Param("userId") Long userId, @Param("countSelective") boolean countSelective);

    List<UserLearningTask> getLearningTask(@Param("userId") Long userId, @Param("bizType") String bizType);

    /**
     * 统计学习人员总数和各状态人数
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return Map包含total(总人数)、completed(已完成人数)、learning(学习中人数)、notStart(未开始人数)
     */
    Map<String, Object> countLearnerStatistics(@Param("bizType") String bizType, @Param("bizId") Long bizId);

    /**
     * 分页查询指定状态的学习人员
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @param status 学习状态
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 学习任务列表
     */
    List<UserLearningTask> queryLearnersByStatus(@Param("bizType") String bizType, 
                                                @Param("bizId") Long bizId, 
                                                @Param("status") String status, 
                                                @Param("offset") Integer offset, 
                                                @Param("pageSize") Integer pageSize);
}
