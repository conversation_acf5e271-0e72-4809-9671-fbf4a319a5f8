package com.learn.infrastructure.repository.mapper;

import com.learn.infrastructure.repository.entity.LearningMapStage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【learning_map_stage(学习地图阶段表)】的数据库操作Mapper
* @createDate 2025-04-17 14:36:42
* @Entity com.learn.infrastructure.repository.entity.LearningMapStage
*/
public interface LearningMapStageMapper extends BaseMapper<LearningMapStage> {

    /**
     * 更新学习地图阶段信息（支持将证书ID设置为null）
     * 
     * @param stage 学习地图阶段实体
     * @return 更新影响的行数
     */
    int updateStageWithNullableFields(@Param("stage") LearningMapStage stage);

}




