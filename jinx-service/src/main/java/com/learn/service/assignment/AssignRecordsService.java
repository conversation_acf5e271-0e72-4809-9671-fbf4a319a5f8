package com.learn.service.assignment;

import com.learn.dto.assignment.AssignRecordDetailQueryRequest;
import com.learn.dto.assignment.AssignRecordDetailQueryResponse;
import com.learn.dto.assignment.AssignRecordsQueryRequest;
import com.learn.dto.assignment.AssignRecordsQueryResponse;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 指派记录服务接口
 * 处理指派记录相关的业务逻辑
 */
public interface AssignRecordsService {

    /**
     * 查询指派记录
     * 根据type_{type_id}查询assign_records中删除状态为false的数据
     * 并根据range_ids查询门店、角色、人员的名称
     *
     * @param request 查询请求参数
     * @return 指派记录列表
     */
    List<AssignRecordsQueryResponse> queryAssignRecords(AssignRecordsQueryRequest request);

    /**
     * 查询指派记录总数
     *
     * @param request 查询请求参数
     * @return 记录总数
     */
    Long countAssignRecords(AssignRecordsQueryRequest request);

    /**
     * 查询指派记录明细
     * 根据指派记录ID查询assignment_detail中的明细
     *
     * @param request 查询请求参数
     * @return 指派记录明细列表
     */
    List<AssignRecordDetailQueryResponse> queryAssignRecordDetails(AssignRecordDetailQueryRequest request);

    /**
     * 查询指派记录明细总数
     *
     * @param request 查询请求参数
     * @return 记录总数
     */
    Long countAssignRecordDetails(AssignRecordDetailQueryRequest request);

    /**
     * 批量统计指派记录的成功和失败人数
     * 
     * @param assignRecordIds 指派记录ID列表
     * @return 指派记录ID -> 统计结果的映射
     */
    Map<Long, Map<String, BigDecimal>> batchCountSuccessAndFailed(List<Long> assignRecordIds);
}
