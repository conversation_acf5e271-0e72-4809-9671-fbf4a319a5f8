
package com.learn.service.assignment.util;

import com.learn.common.enums.AssignFinishedTimeTypeEnums;
import com.learn.common.enums.AssignStatusEnums;
import com.learn.common.enums.AssignTypeEnums;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 指派状态工具类
 * 处理指派状态和完成时间类型的转换逻辑
 */
public class AssignmentStatusUtil {

    /**
     * 获取指派状态
     * 
     * @param assignType 指派类型
     * @param status 当前状态
     * @param deadline 截止时间
     * @return 指派状态代码
     */
    public static String getAssignStatus(String assignType, String status, Date deadline) {
        if (!StringUtils.hasText(assignType) || !StringUtils.hasText(status)) {
            return AssignStatusEnums.WAIT.getCode();
        }

        // auto类型的状态逻辑
        if (AssignTypeEnums.AUTO.getCode().equals(assignType)) {
            if (AssignStatusEnums.WAIT.getCode().equals(status)) {
                return "wait"; // 待调度
            } else if (deadline != null && new Date().before(deadline)) {
                return "process"; // 进行中
            } else {
                return "finished"; // 已结束
            }
        }
        
        // once类型的状态逻辑
        if (AssignTypeEnums.ONCE.getCode().equals(assignType)) {
            if (AssignStatusEnums.WAIT.getCode().equals(status)) {
                return "wait"; // 待调度
            } else if (AssignStatusEnums.SUCCESS.getCode().equals(status)) {
                return "completed"; // 已完成
            } else {
                return "finished"; // 已结束
            }
        }

        return status;
    }

    /**
     * 获取指派状态描述
     * 
     * @param assignStatus 指派状态代码
     * @return 指派状态描述
     */
    public static String getAssignStatusDesc(String assignStatus) {
        if (!StringUtils.hasText(assignStatus)) {
            return "未知状态";
        }

        switch (assignStatus) {
            case "wait":
                return "待调度";
            case "process":
                return "进行中";
            case "finished":
                return "已结束";
            case "completed":
                return "已完成";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取完成时间类型描述
     * 
     * @param assignFinishedTimeType 完成时间类型
     * @param customFinishedDay 自定义天数
     * @return 完成时间类型描述
     */
    public static String getAssignFinishedTimeTypeDesc(String assignFinishedTimeType, Integer customFinishedDay) {
        if (!StringUtils.hasText(assignFinishedTimeType)) {
            return "不设置";
        }

        // 根据code查找对应的枚举
        for (AssignFinishedTimeTypeEnums timeTypeEnum : AssignFinishedTimeTypeEnums.values()) {
            if (timeTypeEnum.getCode().equalsIgnoreCase(assignFinishedTimeType)) {
                if (timeTypeEnum == AssignFinishedTimeTypeEnums.CUSTOM) {
                    if (customFinishedDay != null && customFinishedDay > 0) {
                        return "自定义" + customFinishedDay + "天";
                    } else {
                        return timeTypeEnum.getMessage();
                    }
                } else {
                    return timeTypeEnum.getMessage();
                }
            }
        }

        return "不设置";
    }
}
