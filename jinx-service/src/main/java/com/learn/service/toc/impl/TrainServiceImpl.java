package com.learn.service.toc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.learn.constants.AttributeKey;
import com.learn.constants.BizType;
import com.learn.constants.LearningStatus;
import com.learn.dto.LearnerInfo;
import com.learn.dto.toc.learning.RecordLearningProgressRequest;
import com.learn.dto.toc.train.TrainDetailResponse;
import com.learn.infrastructure.repository.entity.Certificate;
import com.learn.infrastructure.repository.entity.ContentRelation;
import com.learn.infrastructure.repository.entity.Courses;
import com.learn.infrastructure.repository.entity.Train;
import com.learn.infrastructure.repository.entity.User;
import com.learn.infrastructure.repository.entity.UserLearningTask;
import com.learn.infrastructure.repository.mapper.CertificateMapper;
import com.learn.infrastructure.repository.mapper.CoursesMapper;
import com.learn.infrastructure.repository.mapper.ContentRelationMapper;
import com.learn.infrastructure.repository.mapper.TrainMapper;
import com.learn.infrastructure.repository.mapper.UserLearningTaskMapper;
import com.learn.infrastructure.repository.mapper.UserMapper;
import com.learn.service.toc.ContentService;
import com.learn.service.toc.TrainService;
import com.learn.service.user.UserStudyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * C端培训服务实现类
 */
@Service
@Slf4j
public class TrainServiceImpl implements TrainService {

    @Autowired
    private TrainMapper trainMapper;

    @Autowired
    private CertificateMapper certificateMapper;

    @Autowired
    private ContentRelationMapper contentRelationMapper;

    @Autowired
    private CoursesMapper coursesMapper;

    @Autowired
    private UserLearningTaskMapper userLearningTaskMapper;

    @Autowired
    private UserStudyService userStudyService;

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ContentService contentService;

    /**
     * 获取培训详情
     *
     * @param id     培训ID
     * @param userId 当前用户ID
     * @return 培训详情
     */
    @Override
    public TrainDetailResponse getTrainDetail(Long id, Long userId) {
        log.info("获取培训详情，培训ID：{}，用户ID：{}", id, userId);

        // 1. 获取培训基本信息
        Train train = trainMapper.selectById(id);
        if (train == null || train.getIsDel() == 1) {
            log.error("培训不存在或已删除，培训ID：{}", id);
            return null;
        }

        // 2. 构建培训详情响应
        TrainDetailResponse response = new TrainDetailResponse();
        response.setId(train.getId());
        response.setType(BizType.TRAIN);
        response.setName(train.getName());
        response.setCover(train.getCover());
        response.setIntroduction(train.getIntroduction());
        response.setCredit(train.getCredit());

        // 3. 获取证书信息
        setCertificateInfo(response, train);

        // 4. 获取培训内容列表
        setContentInfo(response, train.getId());

        // 5. 获取用户学习进度
        setUserProgress(response, train.getId(), userId);

        return response;
    }

    /**
     * 设置证书信息
     *
     * @param response 响应对象
     * @param train    培训实体
     */
    private void setCertificateInfo(TrainDetailResponse response, Train train) {
        long start = System.currentTimeMillis();
        if (train.getCertificateId() != null) {
            Certificate certificate = certificateMapper.selectById(train.getCertificateId());
            if (certificate != null && certificate.getIsDel() == 0) {
                TrainDetailResponse.CertificateInfo certificateInfo = new TrainDetailResponse.CertificateInfo();
                certificateInfo.setId(certificate.getId());
                certificateInfo.setName(certificate.getName());
                response.setCertificate(certificateInfo);
            }
        }
        System.out.println("setCertificateInfo:耗时:" + (System.currentTimeMillis() - start));
    }

    /**
     * 设置用户学习进度
     *
     * @param response 响应对象
     * @param trainId  培训ID
     * @param userId   用户ID
     */
    private void setUserProgress(TrainDetailResponse response, Long trainId, Long userId) {
        long start = System.currentTimeMillis();
        // 没有关联课程
        if (CollectionUtils.isEmpty(response.getContents())) {
            log.warn("培训下没有关联,培训ID：{}", trainId);
            return;
        }
        // 获取所有内容ID
        List<String> contentSearchKeys = response.getContents().stream()
                .map(v -> v.getContentType() + "_" + v.getContentId())
                .toList();
        // 查询培训的学习任务记录
        RecordLearningProgressRequest request = new RecordLearningProgressRequest();
        request.setContentType(BizType.TRAIN);
        request.setContentId(trainId);
        UserLearningTask trainLearningTask = userStudyService.getLearningTask(userId, request);
        trainLearningTask = Objects.isNull(trainLearningTask) ? new UserLearningTask() : trainLearningTask;


        // 查询培训关联课程的学习记录
        List<String> userSearchKeys = contentSearchKeys.stream()
                .map(v -> v + "_" + userId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<UserLearningTask> courseTaskWrapper = new LambdaQueryWrapper<>();
        courseTaskWrapper.in(UserLearningTask::getSearchKey, userSearchKeys)
                .eq(UserLearningTask::getIsDel, 0);
        List<UserLearningTask> courseTasks = userLearningTaskMapper.selectList(courseTaskWrapper);

        // 设置课程进度
        Map<Long, UserLearningTask> courseTaskMap = courseTasks.stream()
                .collect(Collectors.toMap(UserLearningTask::getBizId, c -> c));
        for (TrainDetailResponse.ContentItem item : response.getContents()) {
            UserLearningTask courseTask = courseTaskMap.get(item.getContentId());
            if (courseTask != null) {
                item.setProgress(courseTask.getProgress());
                item.setStatus(courseTask.convertStatus(courseTask.getProgress()));
            } else {
                item.setProgress(0);
                item.setStatus(LearningStatus.NOT_STARTED);
            }
        }

        // 统计已完成的课程数和总学习时长
        int totalStudyDuration = courseTasks.stream()
                .filter(task -> task.getStudyDuration() != null)
                .mapToInt(UserLearningTask::getStudyDuration)
                .sum();
        // 计算必修课程总数和已完成数
        int requiredTaskTotal = (int) response.getContents().stream()
                .filter(relation -> relation.getIsRequired() == 1)
                .count();
        // 已完成的必修课程数
        int requiredTaskFinished = (int) courseTasks.stream()
                .filter(task -> LearningStatus.COMPLETED.equals(task.getStatus()))
                .count();
        
        // 设置用户进度信息
        TrainDetailResponse.UserProgress userProgress = new TrainDetailResponse.UserProgress();
        
        // 计算进度百分比 - 已完成的课程 / 总课程
        int progress = 0;
        if (requiredTaskTotal > 0) {
            progress = (int) (((double) requiredTaskFinished / requiredTaskTotal) * 100);
        }
        
        // 设置学习状态
        userProgress.setStatus(trainLearningTask.convertStatus(progress));
        userProgress.setProgress(progress);
        userProgress.setStudyDuration(totalStudyDuration);
        userProgress.setRequiredTaskFinished(requiredTaskFinished);
        userProgress.setRequiredTaskTotal(requiredTaskTotal);
        
        response.setUserProgress(userProgress);
        System.out.println("setUserProgress:耗时:" + (System.currentTimeMillis() - start));
    }

    /**
     * 设置培训内容列表
     *
     * @param response 响应对象
     * @param trainId  培训ID
     */
    private void setContentInfo(TrainDetailResponse response, Long trainId) {
        long start = System.currentTimeMillis();
        // 1. 查询培训内容关联
        LambdaQueryWrapper<ContentRelation> contentWrapper = new LambdaQueryWrapper<>();
        contentWrapper.eq(ContentRelation::getBizId, trainId)
                .eq(ContentRelation::getBizType, BizType.TRAIN)
                .eq(ContentRelation::getContentType, BizType.COURSE)
                .eq(ContentRelation::getIsDel, 0)
                .orderByAsc(ContentRelation::getSortOrder);
        List<ContentRelation> contentRelations = contentRelationMapper.selectList(contentWrapper);
        if (CollectionUtils.isEmpty(contentRelations)) {
            response.setContents(Collections.emptyList());
            return;
        }

        // 2. 获取所有内容ID
        List<Long> courseIds = contentRelations.stream()
                .map(ContentRelation::getContentId)
                .collect(Collectors.toList());
        // 批量查询课程信息
        LambdaQueryWrapper<Courses> coursesWrapper = new LambdaQueryWrapper<>();
        coursesWrapper.in(Courses::getId, courseIds).eq(Courses::getIsDel, 0);
        List<Courses> coursesList = coursesMapper.selectList(coursesWrapper);
        Map<Long, Courses> coursesMap = coursesList.stream().collect(Collectors.toMap(Courses::getId, c -> c));

        // 转换为DTO
        List<TrainDetailResponse.ContentItem> contentItems = new ArrayList<>();
        for (ContentRelation relation : contentRelations) {
            Courses course = coursesMap.get(relation.getContentId());
            if (null == course) {
                continue;
            }
            TrainDetailResponse.ContentItem item = new TrainDetailResponse.ContentItem();
            item.setId(relation.getId());
            item.setContentType(relation.getContentType());
            item.setContentId(relation.getContentId());
            item.setIsRequired(relation.getIsRequired());
            item.setTitle(course.getTitle());
            item.setType(course.getType());
            item.setCoverImage(course.getCoverImage());
            contentItems.add(item);
        }

        response.setContents(contentItems);
        System.out.println("setContentInfo:耗时:" + (System.currentTimeMillis() - start));
    }

}
