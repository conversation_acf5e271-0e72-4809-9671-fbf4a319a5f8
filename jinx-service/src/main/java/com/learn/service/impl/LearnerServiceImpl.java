package com.learn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.learn.constants.BizType;
import com.learn.constants.LearningStatus;
import com.learn.dto.LearnerInfo;
import com.learn.dto.LearnerQueryReq;
import com.learn.dto.toc.course.CourseDetailResponse;
import com.learn.dto.toc.train.TrainDetailResponse;
import com.learn.infrastructure.repository.entity.Department;
import com.learn.infrastructure.repository.entity.DepartmentUser;
import com.learn.infrastructure.repository.entity.User;
import com.learn.infrastructure.repository.entity.UserLearningTask;
import com.learn.infrastructure.repository.mapper.DepartmentMapper;
import com.learn.infrastructure.repository.mapper.DepartmentUserMapper;
import com.learn.infrastructure.repository.mapper.UserLearningTaskMapper;
import com.learn.infrastructure.repository.mapper.UserMapper;
import com.learn.service.LearnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */

@Slf4j
@Service
public class LearnerServiceImpl implements LearnerService {
    @Autowired
    private UserLearningTaskMapper userLearningTaskMapper;
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private DepartmentUserMapper departmentUserMapper;
    
    @Autowired
    private DepartmentMapper departmentMapper;

    @Override
    public LearnerInfo total(LearnerQueryReq request) {
        log.info("统计学习人员信息，请求参数：{}", request);
        
        // 参数校验
        if (request == null || StringUtils.isEmpty(request.getBizType()) || request.getBizId() == null) {
            throw new IllegalArgumentException("业务类型和业务ID不能为空");
        }
        
        // 1. 用sql进行统计，sql实现写在 userLearningTaskMapper.xml
        Map<String, Object> statisticsMap = userLearningTaskMapper.countLearnerStatistics(
            request.getBizType(), request.getBizId());
        
        // 2. 封装返回结果，设置total，completed，learning， notStart四个类型的总数
        LearnerInfo learnerInfo = new LearnerInfo();
        if (statisticsMap != null) {
            learnerInfo.setTotal(((Number) statisticsMap.getOrDefault("total", 0)).intValue());
            learnerInfo.setCompleted(((Number) statisticsMap.getOrDefault("completed", 0)).intValue());
            learnerInfo.setLearning(((Number) statisticsMap.getOrDefault("learning", 0)).intValue());
            learnerInfo.setNotStart(((Number) statisticsMap.getOrDefault("notStart", 0)).intValue());
        } else {
            learnerInfo.setTotal(0);
            learnerInfo.setCompleted(0);
            learnerInfo.setLearning(0);
            learnerInfo.setNotStart(0);
        }
        
        log.info("统计学习人员信息完成，结果：{}", learnerInfo);
        return learnerInfo;
    }

    @Override
    public LearnerInfo query(LearnerQueryReq request) {
        log.info("分页查询学习人员信息，请求参数：{}", request);
        // 1. 参数校验
        if (request == null || StringUtils.isEmpty(request.getBizType()) || request.getBizId() == null) {
            throw new IllegalArgumentException("业务类型和业务ID不能为空");
        }
        // 2. 计算偏移量
        int offset = (request.getPageNum() - 1) * request.getPageSize();
        
        // 3. 查询学习任务记录
        List<UserLearningTask> tasks = userLearningTaskMapper.queryLearnersByStatus(
            request.getBizType(), 
            request.getBizId(), 
            request.getStatus(), 
            offset, 
            request.getPageSize()
        );
        
        // 4. 组装结果
        LearnerInfo learnerInfo = new LearnerInfo();
        if (CollectionUtils.isEmpty(tasks)) {
            log.info("未查询到学习人员信息");
            return learnerInfo;
        }
        
        // 5. 获取用户信息
        List<Long> userIds = tasks.stream()
                .map(UserLearningTask::getUserId)
                .collect(Collectors.toList());
        
        Map<Long, User> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userIds)) {
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.in(User::getUserId, userIds).eq(User::getIsDel, 0);
            List<User> users = userMapper.selectList(userWrapper);
            userMap = users.stream().collect(Collectors.toMap(User::getUserId, u -> u, (v1, v2) -> v1));
        }
        
        // 6. 构建学习人员列表
        List<LearnerInfo.LearnerItem> learnerItems = buildLearnerItems(tasks, userMap);
        learnerInfo.setLearnerItems(learnerItems);
        log.info("分页查询学习人员信息完成，返回{}条记录", learnerItems.size());
        return learnerInfo;
    }

    /**
     * 构建学习人员列表项
     *
     * @param tasks 学习任务记录列表
     * @param userMap 用户信息映射
     * @return 学习人员列表
     */
    private List<LearnerInfo.LearnerItem> buildLearnerItems(
            List<UserLearningTask> tasks,
            Map<Long, User> userMap) {

        List<LearnerInfo.LearnerItem> items = new ArrayList<>();

        for (UserLearningTask task : tasks) {
            LearnerInfo.LearnerItem item = new LearnerInfo.LearnerItem();
            item.setUserId(task.getUserId());

            // 设置用户信息
            User user = userMap.get(task.getUserId());
            if (user != null) {
                item.setNickname(user.getNickname());
                item.setAvatar(user.getAvatar());
            } else {
                item.setNickname(task.getCreatorName());
            }

            items.add(item);
        }

        return items;
    }
}
