<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.learn.infrastructure.repository.mapper.LearningMapStageMapper">

    <resultMap id="BaseResultMap" type="com.learn.infrastructure.repository.entity.LearningMapStage">
            <id property="id" column="id" />
            <result property="mapId" column="map_id" />
            <result property="name" column="name" />
            <result property="stageOrder" column="stage_order" />
            <result property="openType" column="open_type" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="durationDays" column="duration_days" />
            <result property="credit" column="credit" />
            <result property="certificateId" column="certificate_id" />
            <result property="gmtCreate" column="gmt_create" />
            <result property="creatorId" column="creator_id" />
            <result property="creatorName" column="creator_name" />
            <result property="updaterId" column="updater_id" />
            <result property="updaterName" column="updater_name" />
            <result property="gmtModified" column="gmt_modified" />
            <result property="isDel" column="is_del" />
    </resultMap>

    <sql id="Base_Column_List">
        id,map_id,name,stage_order,open_type,start_time,
        end_time,duration_days,credit,certificate_id,gmt_create,
        creator_id,creator_name,updater_id,updater_name,gmt_modified,
        is_del
    </sql>

    <!-- 更新学习地图阶段信息（支持将证书ID设置为null） -->
    <update id="updateStageWithNullableFields" parameterType="com.learn.infrastructure.repository.entity.LearningMapStage">
        UPDATE learning_map_stage
        <set>
            <if test="stage.mapId != null">
                map_id = #{stage.mapId},
            </if>
            <if test="stage.name != null and stage.name != ''">
                name = #{stage.name},
            </if>
            <if test="stage.stageOrder != null">
                stage_order = #{stage.stageOrder},
            </if>
            <if test="stage.openType != null">
                open_type = #{stage.openType},
            </if>
            <if test="stage.startTime != null">
                start_time = #{stage.startTime},
            </if>
            <if test="stage.endTime != null">
                end_time = #{stage.endTime},
            </if>
            <if test="stage.durationDays != null">
                duration_days = #{stage.durationDays},
            </if>
            <if test="stage.credit != null">
                credit = #{stage.credit},
            </if>
            <!-- 支持将证书ID设置为null，使用特殊判断条件 -->
            certificate_id = #{stage.certificateId},
            <if test="stage.updaterId != null">
                updater_id = #{stage.updaterId},
            </if>
            <if test="stage.updaterName != null and stage.updaterName != ''">
                updater_name = #{stage.updaterName},
            </if>
            <if test="stage.gmtModified != null">
                gmt_modified = #{stage.gmtModified},
            </if>
            <if test="stage.isDel != null">
                is_del = #{stage.isDel}
            </if>
        </set>
        WHERE id = #{stage.id}
    </update>
</mapper>
