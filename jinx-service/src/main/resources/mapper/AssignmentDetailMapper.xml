<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.learn.infrastructure.repository.mapper.AssignmentDetailMapper">

    <resultMap id="BaseResultMap" type="com.learn.infrastructure.repository.entity.AssignmentDetail">
        <id property="id" column="id" />
        <result property="rangeId" column="range_id" />
        <result property="assignRecordId" column="assign_record_id" />
        <result property="userid" column="userid" />
        <result property="bizId" column="biz_id" />
        <result property="type" column="type" />
        <result property="typeId" column="type_id" />
        <result property="status" column="status" />
        <result property="gmtCreate" column="gmt_create" />
        <result property="attributes" column="attributes" />
        <result property="creatorId" column="creator_id" />
        <result property="creatorName" column="creator_name" />
        <result property="updaterId" column="updater_id" />
        <result property="updaterName" column="updater_name" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="isDel" column="is_del" />
        <result property="finishTime" column="finish_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,range_id,assign_record_id,userid,biz_id,type,type_id,status,
        gmt_create,attributes,creator_id,creator_name,updater_id,
        updater_name,gmt_modified,is_del,finish_time
    </sql>

    <!-- 统计指派记录的成功和失败人数，按record_id+user_id去重 -->
    <select id="countSuccessAndFailedByRecordId" resultType="java.util.Map">
        SELECT 
            COALESCE(SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END), 0) as successCount,
            COALESCE(SUM(CASE WHEN status = '2' THEN 1 ELSE 0 END), 0) as failedCount
        FROM (
            SELECT DISTINCT assign_record_id, userid, status
            FROM assignment_detail 
            WHERE assign_record_id = #{assignRecordId} 
            AND is_del = 0
        ) t
    </select>


    <!-- 批量插入或更新指派明细 -->
    <insert id="batchInsertOrUpdateAssignmentDetail" parameterType="java.util.List">
        INSERT INTO assignment_detail (
        range_id, assign_record_id, userid, biz_id, type, type_id, status,
            gmt_create, attributes, creator_id, creator_name, updater_id, updater_name,
            gmt_modified, is_del, finish_time
        ) VALUES
        <foreach collection="detailList" item="detail" separator=",">
            (
                #{detail.rangeId}, #{detail.assignRecordId}, #{detail.userid}, #{detail.bizId},
                #{detail.type}, #{detail.typeId}, #{detail.status}, #{detail.gmtCreate}, 
                #{detail.attributes}, #{detail.creatorId}, #{detail.creatorName}, 
                #{detail.updaterId}, #{detail.updaterName}, #{detail.gmtModified}, 
                #{detail.isDel}, #{detail.finishTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            status = VALUES(status),
            attributes = VALUES(attributes)
    </insert>
</mapper>
