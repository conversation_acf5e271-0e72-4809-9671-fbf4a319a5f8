
package com.learn.service.assignment;

import com.learn.service.assignment.util.AssignmentStatusUtil;
import org.junit.jupiter.api.Test;

import java.util.Date;

/**
 * 指派状态工具类测试
 */
public class AssignmentStatusUtilTest {

    @Test
    public void testGetAssignStatus() {
        // 测试auto类型
        String status1 = AssignmentStatusUtil.getAssignStatus("auto", "wait", null);
        System.out.println("Auto wait status: " + status1);
        
        String status2 = AssignmentStatusUtil.getAssignStatus("auto", "process", new Date(System.currentTimeMillis() + 86400000));
        System.out.println("Auto process status: " + status2);
        
        // 测试once类型
        String status3 = AssignmentStatusUtil.getAssignStatus("once", "wait", null);
        System.out.println("Once wait status: " + status3);
        
        String status4 = AssignmentStatusUtil.getAssignStatus("once", "success", null);
        System.out.println("Once success status: " + status4);
    }

    @Test
    public void testGetAssignFinishedTimeTypeDesc() {
        String desc1 = AssignmentStatusUtil.getAssignFinishedTimeTypeDesc("one_week", null);
        System.out.println("One week desc: " + desc1);
        
        String desc2 = AssignmentStatusUtil.getAssignFinishedTimeTypeDesc("custom", 30);
        System.out.println("Custom30 days desc: " + desc2);
        
        String desc3 = AssignmentStatusUtil.getAssignFinishedTimeTypeDesc("none", null);
        System.out.println("None desc: " + desc3);
    }
}
